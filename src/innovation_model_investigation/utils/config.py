"""
配置管理模块
负责项目配置参数的管理
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class Config:
    """配置管理器"""
    
    # 默认配置
    DEFAULT_CONFIG = {
        # 数据配置
        'data': {
            'sample_data_path': 'data/enterprise_risk_sample_data.csv',
            'data_dict_path': 'data/data_dictionary.csv',
            'target_column': 'label',
            'test_size': 0.3,
            'random_state': 42
        },
        
        # 数据预处理配置
        'preprocessing': {
            'numerical_strategy': 'median',  # mean, median, mode
            'categorical_strategy': 'mode',  # mode, unknown
            'outlier_method': 'iqr',  # iqr, zscore
            'outlier_factor': 1.5,
            'handle_missing': True,
            'handle_outliers': True
        },
        
        # 分箱配置
        'binning': {
            'min_prebin_size': 0.05,
            'min_n_bins': 3,
            'max_n_bins': 6,
            'min_bin_size': 0.05,
            'max_pvalue': 0.05,
            'gamma': 0.0
        },
        
        # WOE编码配置
        'woe_encoding': {
            'regularization': 1e-6,
            'handle_unknown': 'error'  # error, ignore, return_nan
        },
        
        # 模型训练配置
        'model': {
            'algorithm': 'logistic_regression',
            'cv_folds': 5,
            'scoring': 'roc_auc',
            'param_grid': {
                'C': [0.001, 0.01, 0.1, 1, 10, 100],
                'penalty': ['l1', 'l2'],
                'solver': ['liblinear', 'saga'],
                'max_iter': [1000]
            },
            'class_weight': 'balanced'
        },
        
        # 评估配置
        'evaluation': {
            'metrics': ['accuracy', 'precision', 'recall', 'f1', 'auc', 'ks'],
            'plot_results': True,
            'save_results': True
        },
        
        # 输出配置
        'output': {
            'output_dir': 'outputs',
            'save_model': True,
            'save_encoder': True,
            'save_weights': True,
            'save_evaluation': True,
            'figure_format': 'png',
            'figure_dpi': 300
        },
        
        # 日志配置
        'logging': {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'save_to_file': True,
            'log_file': 'logs/model_training.log'
        }
    }
    
    def __init__(self, config_dict: Optional[Dict[str, Any]] = None):
        """
        初始化配置管理器
        
        Args:
            config_dict: 自定义配置字典
        """
        self.config = self.DEFAULT_CONFIG.copy()
        
        if config_dict:
            self.update_config(config_dict)
        
        # 创建必要的目录
        self._create_directories()
        
        # 设置日志
        self._setup_logging()
    
    def update_config(self, config_dict: Dict[str, Any]) -> None:
        """
        更新配置
        
        Args:
            config_dict: 要更新的配置字典
        """
        def deep_update(base_dict: Dict, update_dict: Dict) -> Dict:
            """深度更新字典"""
            for key, value in update_dict.items():
                if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
            return base_dict
        
        deep_update(self.config, config_dict)
        logger.info("配置已更新")
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，如 'data.sample_data_path'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key_path: 配置键路径，如 'data.sample_data_path'
            value: 要设置的值
        """
        keys = key_path.split('.')
        config = self.config
        
        # 导航到目标位置
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # 设置值
        config[keys[-1]] = value
        logger.info(f"配置 {key_path} 已设置为: {value}")
    
    def _create_directories(self) -> None:
        """创建必要的目录"""
        dirs_to_create = [
            self.get('output.output_dir', 'outputs'),
            'logs',
            'data'
        ]
        
        for dir_path in dirs_to_create:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    def _setup_logging(self) -> None:
        """设置日志"""
        log_level = self.get('logging.level', 'INFO')
        log_format = self.get('logging.format', 
                             '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        
        # 配置根日志器
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format=log_format,
            force=True
        )
        
        # 如果需要保存到文件
        if self.get('logging.save_to_file', True):
            log_file = self.get('logging.log_file', 'logs/model_training.log')
            
            # 确保日志目录存在
            Path(log_file).parent.mkdir(parents=True, exist_ok=True)
            
            # 添加文件处理器
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(getattr(logging, log_level.upper()))
            file_handler.setFormatter(logging.Formatter(log_format))
            
            # 获取根日志器并添加处理器
            root_logger = logging.getLogger()
            root_logger.addHandler(file_handler)
    
    def get_data_config(self) -> Dict[str, Any]:
        """获取数据相关配置"""
        return self.get('data', {})
    
    def get_preprocessing_config(self) -> Dict[str, Any]:
        """获取预处理相关配置"""
        return self.get('preprocessing', {})
    
    def get_binning_config(self) -> Dict[str, Any]:
        """获取分箱相关配置"""
        return self.get('binning', {})
    
    def get_woe_config(self) -> Dict[str, Any]:
        """获取WOE编码相关配置"""
        return self.get('woe_encoding', {})
    
    def get_model_config(self) -> Dict[str, Any]:
        """获取模型相关配置"""
        return self.get('model', {})
    
    def get_evaluation_config(self) -> Dict[str, Any]:
        """获取评估相关配置"""
        return self.get('evaluation', {})
    
    def get_output_config(self) -> Dict[str, Any]:
        """获取输出相关配置"""
        return self.get('output', {})
    
    def save_config(self, filepath: str) -> None:
        """
        保存配置到文件
        
        Args:
            filepath: 配置文件路径
        """
        import json
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
        
        logger.info(f"配置已保存到: {filepath}")
    
    @classmethod
    def load_config(cls, filepath: str) -> 'Config':
        """
        从文件加载配置
        
        Args:
            filepath: 配置文件路径
            
        Returns:
            配置管理器实例
        """
        import json
        
        with open(filepath, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        
        return cls(config_dict)
    
    def __str__(self) -> str:
        """返回配置的字符串表示"""
        import json
        return json.dumps(self.config, ensure_ascii=False, indent=2)
    
    def print_config(self) -> None:
        """打印当前配置"""
        print("="*50)
        print("当前配置:")
        print("="*50)
        print(self)
        print("="*50) 