"""
WOE编码器
将原始特征转换为WOE编码特征
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
import logging

logger = logging.getLogger(__name__)


class WOEEncoder:
    """WOE编码器"""
    
    def __init__(self):
        self.woe_mappings = {}
        self.is_fitted = False
        
    def fit_transform(self, 
                     features: pd.DataFrame, 
                     target: pd.Series,
                     binning_results: Dict) -> pd.DataFrame:
        """
        拟合并转换特征为WOE编码
        
        Args:
            features: 原始特征DataFrame
            target: 目标变量
            binning_results: 分箱结果字典
            
        Returns:
            WOE编码后的特征DataFrame
        """
        logger.info("开始WOE编码转换")
        
        woe_features = pd.DataFrame(index=features.index)
        
        for feature_name in features.columns:
            if feature_name in binning_results:
                woe_values = self._transform_single_feature(
                    features[feature_name], 
                    target,
                    binning_results[feature_name]
                )
                woe_features[feature_name] = woe_values
            else:
                logger.warning(f"特征 {feature_name} 未找到分箱结果，跳过")
                
        self.is_fitted = True
        logger.info(f"WOE编码完成，转换了 {len(woe_features.columns)} 个特征")
        
        return woe_features
    
    def transform(self, features: pd.DataFrame) -> pd.DataFrame:
        """
        使用已拟合的编码器转换新数据
        
        Args:
            features: 待转换的特征DataFrame
            
        Returns:
            WOE编码后的特征DataFrame
        """
        if not self.is_fitted:
            raise ValueError("编码器未拟合，请先调用 fit_transform")
            
        woe_features = pd.DataFrame(index=features.index)
        
        for feature_name in features.columns:
            if feature_name in self.woe_mappings:
                woe_values = self._apply_woe_mapping(
                    features[feature_name],
                    self.woe_mappings[feature_name]
                )
                woe_features[feature_name] = woe_values
            else:
                logger.warning(f"特征 {feature_name} 未找到WOE映射，跳过")
                
        return woe_features
    
    def _transform_single_feature(self, 
                                 feature: pd.Series,
                                 target: pd.Series, 
                                 binning_result: Dict) -> pd.Series:
        """转换单个特征为WOE编码"""
        try:
            # 使用optbinning实例进行转换
            if 'optb_instance' in binning_result and binning_result['optb_instance'] is not None:
                optb = binning_result['optb_instance']
                woe_values = optb.transform(feature.values, metric="woe")
                woe_series = pd.Series(woe_values, index=feature.index, name=feature.name)
                
                # 保存WOE映射关系
                self._save_woe_mapping_from_optb(feature, optb, binning_result)
                
                return woe_series
            else:
                # 使用默认方法计算WOE
                return self._manual_woe_transform(feature, target, binning_result)
                
        except Exception as e:
            logger.error(f"WOE转换失败 {feature.name}: {e}")
            # 使用手动计算方法作为备选
            return self._manual_woe_transform(feature, target, binning_result)
    
    def _manual_woe_transform(self, 
                            feature: pd.Series,
                            target: pd.Series,
                            binning_result: Dict) -> pd.Series:
        """手动计算WOE转换"""
        splits = binning_result.get('splits', [])
        
        if splits is None or len(splits) == 0:
            # 如果没有分割点，返回标准化特征
            return (feature - feature.mean()) / feature.std()
        
        # 创建分箱
        bins = [-np.inf] + list(splits) + [np.inf]
        feature_binned = pd.cut(feature, bins=bins, include_lowest=True)
        
        # 计算每个分箱的WOE
        woe_mapping = {}
        total_good = (target == 0).sum()
        total_bad = (target == 1).sum()
        
        for bin_label in feature_binned.cat.categories:
            mask = feature_binned == bin_label
            bin_good = (target[mask] == 0).sum()
            bin_bad = (target[mask] == 1).sum()
            
            if bin_good > 0 and bin_bad > 0:
                good_rate = bin_good / total_good
                bad_rate = bin_bad / total_bad
                woe = np.log(good_rate / bad_rate)
            else:
                woe = 0.0
                
            woe_mapping[bin_label] = woe
        
        # 应用WOE映射
        woe_values = feature_binned.map(woe_mapping)
        
        # 保存映射关系
        self.woe_mappings[feature.name] = {
            'splits': splits,
            'woe_mapping': woe_mapping
        }
        
        # 处理fillna，将Categorical转换为数值类型
        if hasattr(woe_values, 'cat'):
            # 如果是Categorical类型，先转换为数值
            woe_values = pd.to_numeric(woe_values, errors='coerce')
        
        return woe_values.fillna(0)
    
    def _save_woe_mapping_from_optb(self, 
                                   original_feature: pd.Series,
                                   optb_instance,
                                   binning_result: Dict):
        """从optbinning实例保存WOE映射关系"""
        splits = binning_result.get('splits', [])
        
        if splits is None or len(splits) == 0:
            return
            
        # 创建分箱到WOE的映射
        bins = [-np.inf] + list(splits) + [np.inf]
        feature_binned = pd.cut(original_feature, bins=bins, include_lowest=True)
        
        # 使用optbinning计算每个分箱的WOE值
        woe_mapping = {}
        for bin_label in feature_binned.cat.categories:
            mask = feature_binned == bin_label
            if np.any(mask):
                # 获取该分箱内的代表值
                bin_values = original_feature[mask]
                if len(bin_values) > 0:
                    representative_value = bin_values.iloc[0]
                    woe_value = optb_instance.transform(np.array([representative_value]), metric="woe")[0]
                    woe_mapping[bin_label] = woe_value
        
        self.woe_mappings[original_feature.name] = {
            'splits': splits,
            'woe_mapping': woe_mapping
        }
    
    def _save_woe_mapping(self, 
                         original_feature: pd.Series,
                         woe_feature: pd.Series,
                         binning_result: Dict):
        """保存WOE映射关系"""
        splits = binning_result.get('splits', [])
        
        if splits is None or len(splits) == 0:
            return
            
        # 创建值到WOE的映射
        bins = [-np.inf] + list(splits) + [np.inf]
        feature_binned = pd.cut(original_feature, bins=bins, include_lowest=True)
        
        # 创建分箱到WOE的映射
        woe_mapping = {}
        for bin_label in feature_binned.cat.categories:
            mask = feature_binned == bin_label
            if np.any(mask):
                woe_mapping[bin_label] = woe_feature[mask].iloc[0]
        
        self.woe_mappings[original_feature.name] = {
            'splits': splits,
            'woe_mapping': woe_mapping
        }
    
    def _apply_woe_mapping(self, 
                          feature: pd.Series,
                          mapping: Dict) -> pd.Series:
        """应用已保存的WOE映射"""
        splits = mapping['splits']
        woe_mapping = mapping['woe_mapping']
        
        if splits is None or len(splits) == 0:
            return feature
            
        # 创建分箱
        bins = [-np.inf] + list(splits) + [np.inf]
        feature_binned = pd.cut(feature, bins=bins, include_lowest=True)
        
        # 应用WOE映射
        woe_values = feature_binned.map(woe_mapping)
        
        # 处理fillna，将Categorical转换为数值类型
        if hasattr(woe_values, 'cat'):
            # 如果是Categorical类型，先转换为数值
            woe_values = pd.to_numeric(woe_values, errors='coerce')
        
        return woe_values.fillna(0)
    
    def get_woe_summary(self) -> Dict[str, Any]:
        """
        获取WOE编码摘要
        
        Returns:
            包含每个特征WOE统计信息的字典
        """
        if not self.woe_mappings:
            logger.warning("未找到WOE映射，返回空摘要")
            return {}
            
        summary_data = {}
        
        for feature_name, mapping in self.woe_mappings.items():
            if 'woe_mapping' in mapping and mapping['woe_mapping']:
                woe_values = list(mapping['woe_mapping'].values())
                summary_data[feature_name] = {
                    'n_bins': len(woe_values),
                    'woe_values': woe_values,
                    'woe_min': min(woe_values),
                    'woe_max': max(woe_values),
                    'woe_range': max(woe_values) - min(woe_values),
                    'woe_std': np.std(woe_values)
                }
        return summary_data
    
    def save_encoder(self, filepath: str):
        """保存编码器到文件"""
        import pickle
        
        encoder_data = {
            'woe_mappings': self.woe_mappings,
            'is_fitted': self.is_fitted
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(encoder_data, f)
            
        logger.info(f"WOE编码器已保存到: {filepath}")
    
    def load_encoder(self, filepath: str):
        """从文件加载编码器"""
        import pickle
        
        with open(filepath, 'rb') as f:
            encoder_data = pickle.load(f)
            
        self.woe_mappings = encoder_data['woe_mappings']
        self.is_fitted = encoder_data['is_fitted']
        
        logger.info(f"WOE编码器已加载自: {filepath}") 