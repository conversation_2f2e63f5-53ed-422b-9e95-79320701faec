1. baseline_comparison.py 比 scripts/model_monitor.py 的对比里面少了 数据漂移 的部分，这个部分的主要作用是什么，源数据来自哪里，帮我找到并解决，如果baseline里面没有，就生成他们。 另外 baseline_comparison 就是用来替代  model_monitor 的把？
2. model_compare_traditional.py 还是用来对比最新的模型数据和人工数据的的吧？ 如果是的话需要做一些优化
   1. 比较后的输出内容放在 outputs/<version>/compare_traditional/下面。
   2. 执行这个脚本的时候，目前只是跟当前的 version 对比了一下，可以动态检测一下是否有 outputs/<version>_enhanced, 如果有就也对比一下
   3. 我看 baseline/model_scorecard_compare.csv是不是没必要生成，这个文件在 evaluation/ 下面有
3. baseline_comparison.py对比两个模型的时候，是不是也可以参考  model_compare_traditional 生成一个简单的ROC曲线对比(可能不需要区分数据集那么详细，是不是就用测试集就足够了，或者也是复用 model_compare_traditional.py 的方法，可以指定测试集)