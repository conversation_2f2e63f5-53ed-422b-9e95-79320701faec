#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型性能监控脚本
实现时间维度的模型性能监控，对比不同时期的模型表现
注意：这与 compare_scorecard_performance.py 不同
- compare_scorecard_performance.py: 对比人工分数 vs 模型分数（方法对比）
- model_performance_monitor.py: 对比历史模型 vs 当前模型（时间对比）
"""

import sys
import os
import pandas as pd
import numpy as np
from sklearn.metrics import roc_auc_score, roc_curve
from scipy import stats
import json
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class ModelPerformanceMonitor:
    """模型性能监控器"""
    
    def __init__(self, baseline_config_path="config/baseline_performance.json"):
        self.baseline_config_path = baseline_config_path
        self.baseline_metrics = self.load_baseline_metrics()
        self.alerts = []
        
    def load_baseline_metrics(self):
        """加载基线性能指标"""
        if os.path.exists(self.baseline_config_path):
            with open(self.baseline_config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 默认基线指标
            return {
                "auc": 0.75,
                "ks": 0.30,
                "precision": 0.70,
                "recall": 0.65,
                "f1": 0.67,
                "accuracy": 0.75
            }
    
    def save_baseline_metrics(self, metrics):
        """保存基线性能指标"""
        os.makedirs(os.path.dirname(self.baseline_config_path), exist_ok=True)
        with open(self.baseline_config_path, 'w', encoding='utf-8') as f:
            json.dump(metrics, f, indent=2, ensure_ascii=False)
    
    def calculate_psi(self, expected, actual, bins=10):
        """计算PSI (Population Stability Index)"""
        try:
            # 确保数据为数值型
            expected = pd.Series(expected).astype(float)
            actual = pd.Series(actual).astype(float)
            
            # 基于期望分布创建分箱
            _, bin_edges = np.histogram(expected, bins=bins)
            
            # 计算期望和实际分布
            expected_counts, _ = np.histogram(expected, bins=bin_edges)
            actual_counts, _ = np.histogram(actual, bins=bin_edges)
            
            # 转换为比例
            expected_pct = expected_counts / len(expected)
            actual_pct = actual_counts / len(actual)
            
            # 避免除零错误
            expected_pct = np.where(expected_pct == 0, 1e-6, expected_pct)
            actual_pct = np.where(actual_pct == 0, 1e-6, actual_pct)
            
            # 计算PSI
            psi = np.sum((actual_pct - expected_pct) * np.log(actual_pct / expected_pct))
            
            return psi
        except Exception as e:
            print(f"⚠️ PSI计算失败: {e}")
            return 0.0
    
    def detect_data_drift(self, baseline_data, current_data, features):
        """检测数据漂移"""
        print("\n🔍 检测数据漂移...")
        
        drift_results = {}
        
        for feature in features:
            if feature in baseline_data.columns and feature in current_data.columns:
                try:
                    # 计算PSI
                    psi = self.calculate_psi(baseline_data[feature], current_data[feature])
                    
                    # PSI阈值判断
                    if psi > 0.25:
                        status = "🔴 严重漂移"
                        self.alerts.append(f"特征 {feature} 发生严重数据漂移 (PSI={psi:.4f})")
                    elif psi > 0.1:
                        status = "🟡 轻微漂移"
                        self.alerts.append(f"特征 {feature} 发生轻微数据漂移 (PSI={psi:.4f})")
                    else:
                        status = "🟢 稳定"
                    
                    drift_results[feature] = {
                        'psi': psi,
                        'status': status
                    }
                    
                    print(f"  {feature:<30} PSI={psi:.4f} {status}")
                    
                except Exception as e:
                    print(f"  ⚠️ {feature} 漂移检测失败: {e}")
                    drift_results[feature] = {'psi': 0.0, 'status': '❓ 检测失败'}
        
        return drift_results
    
    def evaluate_current_performance(self, y_true, y_pred, y_prob):
        """评估当前模型性能"""
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
        
        try:
            # 计算各项指标
            auc = roc_auc_score(y_true, y_prob)
            
            # 计算KS值
            fpr, tpr, _ = roc_curve(y_true, y_prob)
            ks = max(tpr - fpr)
            
            accuracy = accuracy_score(y_true, y_pred)
            precision = precision_score(y_true, y_pred, zero_division=0)
            recall = recall_score(y_true, y_pred, zero_division=0)
            f1 = f1_score(y_true, y_pred, zero_division=0)
            
            return {
                'auc': auc,
                'ks': ks,
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1': f1
            }
        except Exception as e:
            print(f"⚠️ 性能评估失败: {e}")
            return None
    
    def compare_with_baseline(self, current_metrics):
        """与基线性能对比"""
        print("\n📊 性能对比分析...")
        
        comparison_results = {}
        
        for metric, current_value in current_metrics.items():
            if metric in self.baseline_metrics:
                baseline_value = self.baseline_metrics[metric]
                diff = current_value - baseline_value
                diff_pct = (diff / baseline_value) * 100 if baseline_value != 0 else 0
                
                # 性能变化判断
                if diff < -0.05:  # 下降超过5%
                    status = "🔴 显著下降"
                    self.alerts.append(f"{metric.upper()} 显著下降: {current_value:.4f} vs {baseline_value:.4f} ({diff_pct:+.1f}%)")
                elif diff < -0.02:  # 下降2-5%
                    status = "🟡 轻微下降"
                    self.alerts.append(f"{metric.upper()} 轻微下降: {current_value:.4f} vs {baseline_value:.4f} ({diff_pct:+.1f}%)")
                elif diff > 0.02:  # 提升超过2%
                    status = "🟢 性能提升"
                else:
                    status = "🔵 基本稳定"
                
                comparison_results[metric] = {
                    'current': current_value,
                    'baseline': baseline_value,
                    'diff': diff,
                    'diff_pct': diff_pct,
                    'status': status
                }
                
                print(f"  {metric.upper():<12} {current_value:.4f} vs {baseline_value:.4f} ({diff_pct:+.1f}%) {status}")
        
        return comparison_results
    
    def generate_monitoring_report(self, current_metrics, drift_results, comparison_results, output_path=None):
        """生成监控报告"""
        if output_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"outputs/monitoring_report_{timestamp}.md"
        
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# 模型性能监控报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 告警摘要
            if self.alerts:
                f.write("## 🚨 告警摘要\n\n")
                for alert in self.alerts:
                    f.write(f"- {alert}\n")
                f.write("\n")
            else:
                f.write("## ✅ 无告警\n\n所有指标均在正常范围内。\n\n")
            
            # 性能对比
            f.write("## 📊 性能指标对比\n\n")
            f.write("| 指标 | 当前值 | 基线值 | 变化 | 变化率 | 状态 |\n")
            f.write("|------|--------|--------|------|--------|------|\n")
            
            for metric, result in comparison_results.items():
                f.write(f"| {metric.upper()} | {result['current']:.4f} | {result['baseline']:.4f} | "
                       f"{result['diff']:+.4f} | {result['diff_pct']:+.1f}% | {result['status']} |\n")
            
            # 数据漂移检测
            f.write("\n## 🔍 数据漂移检测\n\n")
            f.write("| 特征 | PSI值 | 状态 |\n")
            f.write("|------|-------|------|\n")
            
            for feature, result in drift_results.items():
                f.write(f"| {feature} | {result['psi']:.4f} | {result['status']} |\n")
            
            # 建议措施
            f.write("\n## 💡 建议措施\n\n")
            
            if len(self.alerts) == 0:
                f.write("- ✅ 模型运行正常，继续监控\n")
            else:
                if any("严重" in alert for alert in self.alerts):
                    f.write("- 🔴 **立即行动**: 模型性能严重下降或数据严重漂移，建议立即重训练\n")
                if any("轻微" in alert for alert in self.alerts):
                    f.write("- 🟡 **密切关注**: 发现轻微问题，建议增加监控频率\n")
                if any("PSI" in alert for alert in self.alerts):
                    f.write("- 📊 **数据检查**: 检查数据源是否发生变化\n")
                if any("AUC" in alert or "KS" in alert for alert in self.alerts):
                    f.write("- 🎯 **模型优化**: 考虑特征工程或模型调优\n")
            
            f.write("\n---\n*报告由模型性能监控系统自动生成*\n")
        
        print(f"📄 监控报告已保存到: {output_path}")
        return output_path
    
    def send_alerts(self):
        """发送告警（模拟）"""
        if self.alerts:
            print("\n🚨 告警通知:")
            for i, alert in enumerate(self.alerts, 1):
                print(f"  {i}. {alert}")
            
            # 这里可以集成实际的告警系统，如邮件、钉钉、企业微信等
            print("\n📧 告警已发送到相关人员")
        else:
            print("\n✅ 无需发送告警")

def find_latest_compare_data():
    """查找最新的对比数据"""
    outputs_dir = "outputs"
    if not os.path.exists(outputs_dir):
        return None, None
    
    version_dirs = [d for d in os.listdir(outputs_dir) 
                   if os.path.isdir(os.path.join(outputs_dir, d)) and not d.startswith('.')]
    version_dirs = [d for d in version_dirs if any(c.isdigit() for c in d)]
    
    if not version_dirs:
        return None, None
    
    version_dirs.sort(key=lambda s: [int(x) for x in s.split('.') if x.isdigit()], reverse=True)
    
    for v in version_dirs:
        compare_path = os.path.join(outputs_dir, v, "evaluation", "model_scorecard_compare.csv")
        if os.path.exists(compare_path):
            return compare_path, v
    
    return None, None

def main():
    print("🔍 模型性能监控系统")
    print("=" * 50)
    
    # 初始化监控器
    monitor = ModelPerformanceMonitor()
    
    # 查找最新数据
    compare_path, version = find_latest_compare_data()
    if not compare_path:
        print("❌ 未找到模型对比数据！")
        return
    
    print(f"📂 加载数据: {compare_path} (版本: {version})")
    df = pd.read_csv(compare_path)
    
    # 分离测试集数据进行监控
    if 'is_test' in df.columns:
        test_df = df[df['is_test'] == 1].copy()
        train_df = df[df['is_test'] == 0].copy()
    else:
        # 如果没有测试集标记，使用全部数据
        test_df = df.copy()
        train_df = df.copy()
    
    print(f"📊 测试集样本: {len(test_df)} 个")
    
    # 准备监控数据
    y_true = test_df['label']
    y_prob = test_df['model_score']
    
    # 使用最优阈值生成预测
    from sklearn.metrics import roc_curve
    fpr, tpr, thresholds = roc_curve(y_true, y_prob)
    youden = tpr - fpr
    best_idx = np.argmax(youden)
    optimal_threshold = thresholds[best_idx]
    y_pred = (y_prob >= optimal_threshold).astype(int)
    
    # 1. 评估当前性能
    print("\n📈 评估当前模型性能...")
    current_metrics = monitor.evaluate_current_performance(y_true, y_pred, y_prob)
    
    if current_metrics is None:
        print("❌ 性能评估失败！")
        return
    
    # 2. 与基线对比
    comparison_results = monitor.compare_with_baseline(current_metrics)
    
    # 3. 数据漂移检测（使用训练集作为基线，测试集作为当前数据）
    feature_cols = [col for col in df.columns 
                   if col not in ['label', 'model_score', 'is_test', 'enterprise_id', 'traditional_total_score']]
    
    drift_results = monitor.detect_data_drift(train_df, test_df, feature_cols[:10])  # 只检测前10个特征
    
    # 4. 生成监控报告
    report_path = monitor.generate_monitoring_report(
        current_metrics, drift_results, comparison_results
    )
    
    # 5. 发送告警
    monitor.send_alerts()
    
    # 6. 更新基线（如果当前性能更好）
    if current_metrics['auc'] > monitor.baseline_metrics['auc']:
        print(f"\n🎉 模型性能提升！更新基线指标...")
        monitor.save_baseline_metrics(current_metrics)
    
    print(f"\n✅ 监控完成！")
    print(f"📊 当前AUC: {current_metrics['auc']:.4f}")
    print(f"📈 告警数量: {len(monitor.alerts)}")

if __name__ == "__main__":
    main()
