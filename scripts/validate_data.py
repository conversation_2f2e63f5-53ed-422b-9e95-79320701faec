#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据质量验证脚本
"""

import pandas as pd
import numpy as np

def validate_data():
    # 读取数据
    df = pd.read_csv('data/enterprise_risk_sample_data.csv')
    
    print('=== 数据基本信息 ===')
    print(f'数据形状: {df.shape}')
    print(f'列数: {len(df.columns)}')
    print()
    
    print('=== 指标分布验证 ===')
    # 检查科技评分指标
    tech_cols = [col for col in df.columns if col.startswith('tech_') and not col.endswith('_score')]
    print(f'科技指标数量: {len(tech_cols)}')
    
    # 检查发展评分指标  
    dev_cols = [col for col in df.columns if col.startswith('dev_') and not col.endswith('_score')]
    print(f'发展指标数量: {len(dev_cols)}')
    
    # 检查经营评分指标
    oper_cols = [col for col in df.columns if col.startswith('oper_') and not col.endswith('_score')]
    print(f'经营指标数量: {len(oper_cols)}')
    
    # 检查风险调整指标
    risk_cols = [col for col in df.columns if col.startswith('risk_')]
    print(f'风险调整指标数量: {len(risk_cols)}')
    print()
    
    print('=== 评分统计 ===')
    score_cols = ['tech_score', 'dev_score', 'oper_score', 'comprehensive_score']
    for col in score_cols:
        if col in df.columns:
            print(f'{col}: 均值={df[col].mean():.1f}, 标准差={df[col].std():.1f}, 范围=[{df[col].min():.1f}, {df[col].max():.1f}]')
    print()
    
    print('=== 标签分布 ===')
    print(df['label'].value_counts())
    print()
    
    print('=== 等级分布 ===')
    print(df['innovation_health_level'].value_counts())
    print()
    
    print('=== 企业规模与评分关系 ===')
    size_score = df.groupby('enterprise_size')['comprehensive_score'].agg(['mean', 'std', 'count'])
    print(size_score)
    print()
    
    print('=== 行业与评分关系 ===')
    industry_score = df.groupby('industry_code')['comprehensive_score'].agg(['mean', 'std', 'count'])
    print(industry_score)
    print()
    
    print('=== 数据质量检查 ===')
    # 检查缺失值
    missing_count = df.isnull().sum().sum()
    print(f'缺失值总数: {missing_count}')
    
    # 检查重复值
    duplicate_count = df.duplicated().sum()
    print(f'重复行数: {duplicate_count}')
    
    # 检查评分范围
    score_issues = []
    for col in score_cols:
        if col in df.columns:
            if df[col].min() < 0 or df[col].max() > 100:
                score_issues.append(f'{col}: 范围超出[0,100]')
    
    if score_issues:
        print('评分范围问题:')
        for issue in score_issues:
            print(f'  {issue}')
    else:
        print('评分范围正常')
    
    print()
    print('✅ 数据质量验证完成')

if __name__ == "__main__":
    validate_data()
