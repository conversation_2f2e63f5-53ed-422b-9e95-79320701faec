#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
样本质量与建模潜力评估脚本
专注于分析样本数据的关键特征与标签相关性、风险分层单调性、预估AUC等，帮助判断数据是否适合建模及修复效果。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_analysis_utils import (
    load_data, calculate_feature_correlations, 
    analyze_risk_stratification, evaluate_model_potential
)
import numpy as np
import pandas as pd

def df_to_markdown_table(df: pd.DataFrame) -> str:
    """
    将DataFrame转换为markdown表格字符串
    """
    headers = '| ' + ' | '.join(df.columns) + ' |'
    sep = '| ' + ' | '.join(['---'] * len(df.columns)) + ' |'
    rows = ['| ' + ' | '.join([str(x) for x in row]) + ' |' for row in df.values]
    return '\n'.join([headers, sep] + rows)

def main():
    output_dir = 'outputs/sample_data/quality/'
    os.makedirs(output_dir, exist_ok=True)
    print("🔍 验证数据修复效果...")
    report_lines = []
    report_lines.append("# 数据质量评估报告\n")
    report_lines.append("> 本报告自动生成，内容涵盖特征相关性、风险分层、模型潜力评估及优化建议。\n")
    # 加载数据
    df = load_data()
    # 1. 检查关键特征与标签的相关性
    print("\n📊 关键特征与标签相关性（修复后）:")
    report_lines.append("## 📊 关键特征与标签相关性（修复后）\n")
    key_features = [
        'financial_ratio_debt',
        'financial_ratio_current', 
        'financial_cash_flow',
        'financial_growth_revenue',
        'operation_market_share',
        'operation_innovation_capability',
        'external_regulatory_compliance'
    ]
    correlations = calculate_feature_correlations(df, key_features)
    # 输出为markdown表格
    corr_df = pd.DataFrame(correlations, columns=["特征", "相关系数"])
    print(corr_df)
    report_lines.append(df_to_markdown_table(corr_df) + '\n')
    # 2. 检查综合风险得分与标签的关系
    risk_label_corr = np.corrcoef(df['comprehensive_risk_score'], df['label'])[0,1]
    line = f"综合风险得分与标签相关性: {risk_label_corr:.4f}"
    print(f"\n🎯 {line}")
    report_lines.append(f"## 🎯 综合风险得分与标签相关性\n\n{risk_label_corr:.4f}\n")
    # 3. 按风险分层分析标签分布
    print("\n📈 按风险分层的标签分布:")
    report_lines.append("## 📈 按风险分层的标签分布\n")
    risk_analysis = analyze_risk_stratification(df)
    if isinstance(risk_analysis, pd.DataFrame):
        print(risk_analysis)
        report_lines.append(df_to_markdown_table(risk_analysis) + '\n')
    else:
        print(risk_analysis)
        report_lines.append(str(risk_analysis) + '\n')
    # 4. 评估数据质量改善
    evaluation = evaluate_model_potential(correlations, risk_label_corr)
    print(f"\n✅ 数据质量评估:")
    report_lines.append("## ✅ 数据质量评估\n")
    eval_items = [
        ("最强特征相关性", f"{evaluation['max_correlation']:.4f}"),
        ("综合风险得分相关性", f"{evaluation['risk_score_correlation']:.4f}"),
        ("风险分层是否合理", '是' if risk_analysis['坏企业比例'].is_monotonic_decreasing else '否'),
        ("强相关特征数量", str(evaluation['strong_features_count'])),
    ]
    eval_table = pd.DataFrame(eval_items, columns=["指标", "结果"])
    print(eval_table)
    report_lines.append(df_to_markdown_table(eval_table) + '\n')
    if evaluation['strong_features']:
        print(f"  - 强相关特征: {evaluation['strong_features']}")
        report_lines.append(f"**强相关特征**: {evaluation['strong_features']}\n")
    # 5. 预期模型性能改善
    print(f"\n🚀 预期模型性能改善:")
    report_lines.append("## 🚀 预期模型性能改善\n")
    performance_icons = {
        'excellent': '🌟',
        'good': '✅', 
        'fair': '⚠️',
        'poor': '❌'
    }
    icon = performance_icons.get(evaluation['performance_level'], '❓')
    perf_line = f"{icon} {evaluation['auc_prediction']}"
    print(f"  {perf_line}")
    report_lines.append(f"{perf_line}\n")
    # 6. 修复建议
    if evaluation['performance_level'] in ['poor', 'fair']:
        print(f"\n💡 进一步优化建议:")
        report_lines.append("## 💡 进一步优化建议\n")
        suggestions = []
        if evaluation['max_correlation'] < 0.3:
            print("  - 增强特征与标签的因果关系")
            suggestions.append("增强特征与标签的因果关系")
        if evaluation['risk_score_correlation'] < 0.4:
            print("  - 优化综合风险得分计算逻辑")
            suggestions.append("优化综合风险得分计算逻辑")
        if evaluation['strong_features_count'] == 0:
            print("  - 减少数据生成中的随机噪音")
            suggestions.append("减少数据生成中的随机噪音")
        for s in suggestions:
            report_lines.append(f"- {s}")
        report_lines.append('')
    else:
        print(f"\n🎉 数据质量已达到{evaluation['performance_level']}水平，可以进行模型训练！")
        report_lines.append(f"\n🎉 数据质量已达到{evaluation['performance_level']}水平，可以进行模型训练！\n")
    print(f"\n所有评估结果已输出到 {output_dir} 目录（如需保存为文件可自行扩展保存逻辑）")
    report_lines.append(f"\n所有评估结果已输出到 `{output_dir}` 目录，并已保存为 `data_quality_report.md` 文件\n")
    # 保存为 markdown 文件
    report_path = os.path.join(output_dir, 'data_quality_report.md')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))

if __name__ == "__main__":
    main() 