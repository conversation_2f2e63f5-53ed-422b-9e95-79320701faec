#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业科创健康行评估样例数据生成器

基于真实的科创健康行指标体系生成企业样本数据，用于模型训练
包含科技评分、发展评分、经营评分等多维度指标
"""

import pandas as pd
import numpy as np
from datetime import datetime
import random
from pathlib import Path

# 设置随机种子确保结果可复现
np.random.seed(42)
random.seed(42)

def calculate_score_from_range(value, score_ranges):
    """
    根据评分区间计算得分

    Args:
        value: 指标值
        score_ranges: 评分区间列表，格式为[(min_val, max_val, score), ...]

    Returns:
        对应的得分
    """
    for min_val, max_val, score in score_ranges:
        if min_val <= value < max_val:
            return score
    return 0

def generate_enterprise_sample_data(n_samples=2000, output_dir="data"):
    """
    生成企业风险评估样例数据
    
    Args:
        n_samples: 生成样本数量，默认2000
        output_dir: 输出目录
    """
    
    # 创建输出目录
    Path(output_dir).mkdir(exist_ok=True)
    
    # 定义30个风险指标及其含义
    risk_indicators = {
        # 财务健康度指标 (1-10)
        'financial_ratio_current': '流动比率',
        'financial_ratio_quick': '速动比率', 
        'financial_ratio_debt': '资产负债率',
        'financial_ratio_equity': '净资产收益率',
        'financial_ratio_profit': '销售净利率',
        'financial_cash_flow': '现金流量比率',
        'financial_turnover_asset': '总资产周转率',
        'financial_turnover_inventory': '存货周转率',
        'financial_growth_revenue': '营收增长率',
        'financial_stability_ebitda': 'EBITDA利润率',
        
        # 经营管理指标 (11-20)
        'operation_market_share': '市场份额',
        'operation_customer_concentration': '客户集中度',
        'operation_supplier_relationship': '供应商关系稳定性',
        'operation_product_diversity': '产品多样性',
        'operation_innovation_capability': '创新能力',
        'operation_quality_management': '质量管理水平',
        'operation_brand_value': '品牌价值',
        'operation_management_efficiency': '管理效率',
        'operation_employee_stability': '员工稳定性',
        'operation_digitalization_level': '数字化水平',
        
        # 外部环境指标 (21-30)
        'external_industry_position': '行业地位',
        'external_policy_sensitivity': '政策敏感度',
        'external_market_volatility': '市场波动性',
        'external_regulatory_compliance': '合规性',
        'external_environmental_impact': '环保影响',
        'external_social_responsibility': '社会责任',
        'external_technology_adaptation': '技术适应性',
        'external_economic_dependency': '经济依赖度',
        'external_geographic_risk': '地理风险',
        'external_competitive_pressure': '竞争压力'
    }
    
    # 生成企业基本信息
    enterprises = []
    for i in range(n_samples):
        enterprise = {
            'enterprise_id': f'ENT_{i+1:06d}',
            'enterprise_name': f'企业_{i+1}号',
            'industry_code': random.choice(['01', '02', '03', '04', '05']),  # 5个行业
            'region_code': random.choice(['A', 'B', 'C', 'D']),  # 4个地区
            'enterprise_size': random.choice(['小微', '小型', '中型', '大型']),
            'establish_years': random.randint(1, 50)
        }
        enterprises.append(enterprise)
    
    # 定义不同企业规模的基础风险水平
    size_risk_base = {
        '小微': 45,  # 基础风险较高
        '小型': 55,  # 中等风险
        '中型': 65,  # 较低风险
        '大型': 75   # 最低风险
    }
    
    # 定义行业风险调整系数
    industry_risk_factor = {
        '01': 1.1,   # 高风险行业
        '02': 1.0,   # 中等风险
        '03': 0.95,  # 较低风险
        '04': 0.9,   # 低风险行业
        '05': 1.05   # 中高风险
    }
    
    # 生成风险指标数据
    data_rows = []
    
    for ent in enterprises:
        row = ent.copy()
        
        # 根据企业规模确定基础风险水平
        base_score = size_risk_base[ent['enterprise_size']]
        industry_factor = industry_risk_factor[ent['industry_code']]
        
        # 为每个风险指标生成分数
        for indicator_code, indicator_name in risk_indicators.items():
            
            # 不同类型指标有不同的生成逻辑
            if indicator_code.startswith('financial'):
                # 财务指标：与企业规模和行业相关性较强
                mean_score = base_score * industry_factor
                std_dev = 15
                
            elif indicator_code.startswith('operation'):
                # 经营指标：更多随机性，但仍与规模相关
                mean_score = base_score * 0.95 + random.gauss(0, 5)
                std_dev = 18
                
            else:  # external
                # 外部环境指标：主要受行业影响
                mean_score = 60 * industry_factor + random.gauss(0, 10)
                std_dev = 20
            
            # 生成指标分数（0-100分）
            score = np.random.normal(mean_score, std_dev)
            # 确保分数在合理范围内
            score = max(0, min(100, score))
            row[indicator_code] = round(score, 2)
        
        # 计算传统加权总分（假设等权重）
        indicator_scores = [row[code] for code in risk_indicators.keys()]
        traditional_score = np.mean(indicator_scores)
        row['traditional_total_score'] = round(traditional_score, 2)
        
        # 生成企业好坏标签（基于真实风险逻辑）
        # 计算真实风险概率（考虑指标间的非线性关系）
        
        # 关键财务指标（正确处理负债率）
        debt_ratio_reversed = 100 - row['financial_ratio_debt']  # 负债率反转
        financial_health = np.mean([
            debt_ratio_reversed,              # 反转后的负债率（越低越好）
            row['financial_ratio_current'],   # 流动比率（越高越好）
            row['financial_cash_flow'],       # 现金流（越高越好）
            row['financial_growth_revenue'],  # 营收增长（越高越好）
            row['financial_ratio_profit']     # 盈利能力（越高越好）
        ])
        
        # 经营管理能力
        operation_capability = np.mean([
            row['operation_market_share'],
            row['operation_innovation_capability'],
            row['operation_management_efficiency'],
            row['operation_quality_management']
        ])
        
        # 外部环境适应性
        external_adaptation = np.mean([
            row['external_regulatory_compliance'],
            row['external_technology_adaptation'],
            row['external_competitive_pressure'],
            row['external_industry_position']
        ])
        
        # 综合风险得分（基于业务逻辑的加权）
        comprehensive_risk = (
            financial_health * 0.5 +         # 财务健康最重要
            operation_capability * 0.3 +     # 经营能力次之
            external_adaptation * 0.2         # 外部环境影响较小
        )
        
        # 添加适度的随机扰动（减少噪音）
        comprehensive_risk += random.gauss(0, 3)
        
        # 根据综合风险得分确定好坏标签（更严格的分层）
        # 分数越低风险越高（坏企业概率越大）
        if comprehensive_risk < 35:
            bad_probability = 0.9   # 极高风险
        elif comprehensive_risk < 45:
            bad_probability = 0.75  # 高风险
        elif comprehensive_risk < 55:
            bad_probability = 0.5   # 中等风险
        elif comprehensive_risk < 65:
            bad_probability = 0.25  # 较低风险
        elif comprehensive_risk < 75:
            bad_probability = 0.1   # 低风险
        else:
            bad_probability = 0.02  # 极低风险
        
        # 生成标签（0=好企业，1=坏企业）
        is_bad = 1 if random.random() < bad_probability else 0
        row['label'] = is_bad
        row['comprehensive_risk_score'] = round(comprehensive_risk, 2)
        
        data_rows.append(row)
    
    # 创建DataFrame
    df = pd.DataFrame(data_rows)
    
    # 添加数据生成时间戳
    df['data_generate_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 保存到CSV文件
    output_file = Path(output_dir) / "enterprise_risk_sample_data.csv"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    # 生成数据字典
    data_dict = []
    for code, name in risk_indicators.items():
        data_dict.append({
            'indicator_code': code,
            'indicator_name': name,
            'data_type': 'float',
            'value_range': '0-100',
            'description': f'{name}评分，分数越高表示该维度风险越低'
        })
    
    # 添加其他字段说明
    other_fields = [
        {'indicator_code': 'enterprise_id', 'indicator_name': '企业ID', 'data_type': 'string', 'value_range': 'ENT_XXXXXX', 'description': '企业唯一标识符'},
        {'indicator_code': 'enterprise_name', 'indicator_name': '企业名称', 'data_type': 'string', 'value_range': '企业_X号', 'description': '企业名称'},
        {'indicator_code': 'industry_code', 'indicator_name': '行业代码', 'data_type': 'string', 'value_range': '01-05', 'description': '企业所属行业编码'},
        {'indicator_code': 'region_code', 'indicator_name': '地区代码', 'data_type': 'string', 'value_range': 'A-D', 'description': '企业所在地区编码'},
        {'indicator_code': 'enterprise_size', 'indicator_name': '企业规模', 'data_type': 'string', 'value_range': '小微/小型/中型/大型', 'description': '企业规模分类'},
        {'indicator_code': 'establish_years', 'indicator_name': '成立年限', 'data_type': 'int', 'value_range': '1-50', 'description': '企业成立年限'},
        {'indicator_code': 'traditional_total_score', 'indicator_name': '传统总分', 'data_type': 'float', 'value_range': '0-100', 'description': '传统等权重加权总分'},
        {'indicator_code': 'comprehensive_risk_score', 'indicator_name': '综合风险得分', 'data_type': 'float', 'value_range': '0-100', 'description': '基于复杂逻辑计算的真实风险得分'},
        {'indicator_code': 'label', 'indicator_name': '企业标签', 'data_type': 'int', 'value_range': '0/1', 'description': '企业好坏标签，0=好企业，1=坏企业'},
        {'indicator_code': 'data_generate_time', 'indicator_name': '数据生成时间', 'data_type': 'datetime', 'value_range': 'YYYY-MM-DD HH:MM:SS', 'description': '数据生成的时间戳'}
    ]
    
    data_dict.extend(other_fields)
    dict_df = pd.DataFrame(data_dict)
    dict_file = Path(output_dir) / "data_dictionary.csv"
    dict_df.to_csv(dict_file, index=False, encoding='utf-8-sig')
    
    # 生成数据概要报告
    print("=" * 60)
    print("🎯 企业风险评估样例数据生成完成")
    print("=" * 60)
    print(f"📊 样本数量: {len(df):,} 个企业")
    print(f"📈 指标数量: {len(risk_indicators)} 个风险指标")
    print(f"📁 输出路径: {output_file}")
    print(f"📚 数据字典: {dict_file}")
    print()
    
    # 标签分布统计
    label_stats = df['label'].value_counts()
    print("📋 标签分布:")
    print(f"   好企业 (0): {label_stats[0]:,} 个 ({label_stats[0]/len(df)*100:.1f}%)")
    print(f"   坏企业 (1): {label_stats[1]:,} 个 ({label_stats[1]/len(df)*100:.1f}%)")
    print()
    
    # 企业规模分布
    size_stats = df['enterprise_size'].value_counts()
    print("🏢 企业规模分布:")
    for size, count in size_stats.items():
        print(f"   {size}: {count:,} 个 ({count/len(df)*100:.1f}%)")
    print()
    
    # 行业分布
    industry_stats = df['industry_code'].value_counts()
    print("🏭 行业分布:")
    for industry, count in industry_stats.items():
        print(f"   行业{industry}: {count:,} 个 ({count/len(df)*100:.1f}%)")
    print()
    
    # 风险指标统计示例
    print("📊 部分关键指标统计:")
    key_indicators = ['financial_ratio_debt', 'financial_cash_flow', 'operation_market_share']
    for indicator in key_indicators:
        mean_val = df[indicator].mean()
        std_val = df[indicator].std()
        print(f"   {risk_indicators[indicator]}: 均值={mean_val:.1f}, 标准差={std_val:.1f}")
    
    print()
    print("✅ 数据生成完成！可以开始进行WOE分箱和模型训练了。")
    print("=" * 60)
    
    return df, dict_df

if __name__ == "__main__":
    # 生成样例数据
    df, dict_df = generate_enterprise_sample_data(n_samples=2000) 