#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业科创健康行评估样例数据生成器

基于真实的科创健康行指标体系生成企业样本数据，用于模型训练
包含科技评分、发展评分、经营评分等多维度指标
"""

import pandas as pd
import numpy as np
from datetime import datetime
import random
from pathlib import Path

# 设置随机种子确保结果可复现
np.random.seed(42)
random.seed(42)

def calculate_score_from_range(value, score_ranges):
    """
    根据评分区间计算得分
    
    Args:
        value: 指标值
        score_ranges: 评分区间列表，格式为[(min_val, max_val, score), ...]
    
    Returns:
        对应的得分
    """
    for min_val, max_val, score in score_ranges:
        if min_val <= value < max_val:
            return score
    return 0

def generate_enterprise_sample_data(n_samples=2000, output_dir="data"):
    """
    生成企业科创健康行评估样例数据
    
    Args:
        n_samples: 生成样本数量，默认2000
        output_dir: 输出目录
    """
    
    # 创建输出目录
    Path(output_dir).mkdir(exist_ok=True)
    
    # 生成企业基本信息
    enterprises = []
    for i in range(n_samples):
        enterprise = {
            'enterprise_id': f'ENT_{i+1:06d}',
            'enterprise_name': f'企业_{i+1}号',
            'industry_code': random.choice(['01', '02', '03', '04', '05']),  # 5个行业
            'region_code': random.choice(['A', 'B', 'C', 'D']),  # 4个地区
            'enterprise_size': random.choice(['小微', '小型', '中型', '大型']),
            'establish_years': random.randint(1, 50)
        }
        enterprises.append(enterprise)
    
    # 定义不同企业规模的基础参数
    size_params = {
        '小微': {'patent_base': 2, 'employee_base': 20, 'capital_base': 100},
        '小型': {'patent_base': 8, 'employee_base': 80, 'capital_base': 500},
        '中型': {'patent_base': 25, 'employee_base': 200, 'capital_base': 2000},
        '大型': {'patent_base': 80, 'employee_base': 800, 'capital_base': 10000}
    }
    
    # 定义行业调整系数
    industry_factors = {
        '01': {'tech_factor': 1.2, 'dev_factor': 1.0, 'oper_factor': 0.9},  # 高科技行业
        '02': {'tech_factor': 1.0, 'dev_factor': 1.1, 'oper_factor': 1.0},  # 制造业
        '03': {'tech_factor': 0.8, 'dev_factor': 1.0, 'oper_factor': 1.1},  # 服务业
        '04': {'tech_factor': 0.6, 'dev_factor': 0.9, 'oper_factor': 1.2},  # 传统行业
        '05': {'tech_factor': 1.1, 'dev_factor': 1.2, 'oper_factor': 0.8}   # 新兴行业
    }
    
    # 生成样本数据
    data_rows = []
    
    for ent in enterprises:
        row = ent.copy()
        
        # 获取企业参数
        size_param = size_params[ent['enterprise_size']]
        industry_factor = industry_factors[ent['industry_code']]
        
        # 生成基础数据
        patent_total = max(0, int(np.random.poisson(size_param['patent_base'] * industry_factor['tech_factor'])))
        patent_authorized = max(0, int(patent_total * np.random.beta(2, 3)))  # 授权率通常较低
        patent_valid = max(0, int(patent_authorized * np.random.beta(3, 2)))  # 维持率较高
        software_copyright = max(0, int(np.random.poisson(size_param['patent_base'] * 0.5)))
        
        # 1. 科技评分指标计算
        # 发明专利申请占比 (假设总专利中发明专利占比)
        total_patents = max(1, patent_total + int(np.random.poisson(patent_total * 0.3)))  # 包含实用新型等
        patent_application_ratio = (patent_total / total_patents) * 100
        tech_patent_application_ratio = calculate_score_from_range(
            patent_application_ratio,
            [(75, 100, 8), (50, 75, 6), (25, 50, 4), (0, 25, 2), (0, 0, 0)]
        )
        
        # 发明专利申请驳回率 (反向指标)
        rejection_rate = np.random.beta(2, 5) * 100  # 驳回率通常较低
        tech_patent_rejection_rate = calculate_score_from_range(
            rejection_rate,
            [(0, 10, 8), (10, 20, 6), (20, 40, 4), (40, 60, 2), (60, 80, 1), (80, 100, 0)]
        )
        
        # 发明专利申请稳定性 (基于变异系数)
        cv = np.random.exponential(0.3)  # 变异系数
        tech_patent_application_stability = calculate_score_from_range(
            cv,
            [(0, 0.25, 5), (0.25, 0.5, 4), (0.5, 0.75, 2), (0.75, float('inf'), 1)]
        )
        
        # 发明专利授权率
        if patent_total > 0:
            authorization_rate = (patent_authorized / patent_total) * 100
        else:
            authorization_rate = 0
        tech_patent_authorization_rate = calculate_score_from_range(
            authorization_rate,
            [(75, 100, 8), (50, 75, 6), (25, 50, 4), (0, 25, 2)]
        )
        
        # 发明专利授权维持率
        if patent_authorized > 0:
            maintenance_rate = (patent_valid / patent_authorized) * 100
        else:
            maintenance_rate = 0
        tech_patent_maintenance_rate = calculate_score_from_range(
            maintenance_rate,
            [(80, 100, 8), (60, 80, 6), (40, 60, 4), (20, 40, 2), (0, 20, 1)]
        )
        
        # 其他科技指标 (简化处理)
        tech_patent_authorization_stability = random.choice([1, 2, 4, 5])
        tech_patent_authorization_ranking = random.choice([1, 2, 3, 4, 5])
        tech_software_copyright_ranking = random.choice([1, 2, 3, 4, 5])
        tech_patent_concentration = random.choice([1, 2, 3])
        tech_external_patent_ratio = random.choice([1, 2, 3])
        tech_patent_continuity = random.choice([0, 1, 3, 6, 8])
        
        # 科技评分调整指标
        tech_adj_patent_outflow = random.choice([0, -5]) if random.random() < 0.1 else 0
        tech_adj_pct_patent = 2 if random.random() < 0.2 else 0
        tech_adj_ip_pledge = 2 if random.random() < 0.15 else 0
        tech_adj_ip_transformation = 5 if random.random() < 0.1 else 0
        tech_adj_tech_achievement = 5 if random.random() < 0.2 else 0
        
        # 2. 发展评分指标
        # 人才招聘稳定性
        talent_stability_cv = np.random.exponential(0.4)
        dev_talent_stability = calculate_score_from_range(
            talent_stability_cv,
            [(0, 0.5, 5), (0.5, 1.0, 3), (1.0, float('inf'), 1)]
        )
        
        # 股权融资
        financing_prob = np.random.random()
        if financing_prob < 0.05:
            dev_equity_financing = 5  # A类投资者
        elif financing_prob < 0.15:
            dev_equity_financing = 3  # B类投资者
        else:
            dev_equity_financing = 0  # C类或无投资
        
        # 企业荣誉
        honor_count = max(0, int(np.random.poisson(size_param['patent_base'] * 0.1)))
        dev_enterprise_honor = calculate_score_from_range(
            honor_count,
            [(11, float('inf'), 10), (7, 11, 8), (4, 7, 6), (1, 4, 2), (0, 1, 0)]
        )
        
        # 发展评分调整
        dev_adj_employee_shareholding = 2 if random.random() < 0.3 else 0
        dev_adj_honor_cancellation = -5 if random.random() < 0.05 else 0
        
        # 3. 经营评分指标
        # 注册资本实缴比例
        paid_ratio = np.random.beta(3, 1) * 100  # 大多数企业实缴比例较高
        oper_capital_paid_ratio = calculate_score_from_range(
            paid_ratio,
            [(100, 100, 5), (80, 100, 4), (60, 80, 3), (40, 60, 2), (0, 40, 1)]
        )
        
        # 关键人员变动
        personnel_changes = max(0, int(np.random.poisson(1.5)))
        oper_key_personnel_change = calculate_score_from_range(
            personnel_changes,
            [(0, 1, 5), (1, 2, 3), (2, 3, 1), (3, 5, 1), (5, float('inf'), 0)]
        )
        
        # 股权变更频率
        equity_changes = max(0, int(np.random.poisson(1.0)))
        oper_equity_change_frequency = calculate_score_from_range(
            equity_changes,
            [(0, 3, 5), (3, 6, 3), (6, float('inf'), 0)]
        )
        
        # 经营评分调整指标
        oper_adj_capital_reduction = random.choice([0, -4, -6, -8, -10]) if random.random() < 0.1 else 0
        oper_adj_equity_structure = -2 if random.random() < 0.05 else 0
        oper_adj_related_party_change = -2 if random.random() < 0.08 else 0
        oper_adj_business_status = -2 if random.random() < 0.03 else 0
        oper_adj_revocation = -2 if random.random() < 0.01 else 0
        
        # 4. 风险评分调整指标
        risk_adj_execution_restriction = -5 if random.random() < 0.02 else 0
        risk_adj_financial_litigation = -5 if random.random() < 0.03 else 0
        risk_adj_environmental_penalty = -2 if random.random() < 0.05 else 0
        risk_adj_tax_arrears = -5 if random.random() < 0.02 else 0
        
        # 将所有指标添加到行数据中
        row.update({
            'tech_patent_application_ratio': tech_patent_application_ratio,
            'tech_patent_rejection_rate': tech_patent_rejection_rate,
            'tech_patent_application_stability': tech_patent_application_stability,
            'tech_patent_authorization_rate': tech_patent_authorization_rate,
            'tech_patent_maintenance_rate': tech_patent_maintenance_rate,
            'tech_patent_authorization_stability': tech_patent_authorization_stability,
            'tech_patent_authorization_ranking': tech_patent_authorization_ranking,
            'tech_software_copyright_ranking': tech_software_copyright_ranking,
            'tech_patent_concentration': tech_patent_concentration,
            'tech_external_patent_ratio': tech_external_patent_ratio,
            'tech_patent_continuity': tech_patent_continuity,
            'tech_adj_patent_outflow': tech_adj_patent_outflow,
            'tech_adj_pct_patent': tech_adj_pct_patent,
            'tech_adj_ip_pledge': tech_adj_ip_pledge,
            'tech_adj_ip_transformation': tech_adj_ip_transformation,
            'tech_adj_tech_achievement': tech_adj_tech_achievement,
            'dev_talent_stability': dev_talent_stability,
            'dev_equity_financing': dev_equity_financing,
            'dev_enterprise_honor': dev_enterprise_honor,
            'dev_adj_employee_shareholding': dev_adj_employee_shareholding,
            'dev_adj_honor_cancellation': dev_adj_honor_cancellation,
            'oper_capital_paid_ratio': oper_capital_paid_ratio,
            'oper_key_personnel_change': oper_key_personnel_change,
            'oper_equity_change_frequency': oper_equity_change_frequency,
            'oper_adj_capital_reduction': oper_adj_capital_reduction,
            'oper_adj_equity_structure': oper_adj_equity_structure,
            'oper_adj_related_party_change': oper_adj_related_party_change,
            'oper_adj_business_status': oper_adj_business_status,
            'oper_adj_revocation': oper_adj_revocation,
            'risk_adj_execution_restriction': risk_adj_execution_restriction,
            'risk_adj_financial_litigation': risk_adj_financial_litigation,
            'risk_adj_environmental_penalty': risk_adj_environmental_penalty,
            'risk_adj_tax_arrears': risk_adj_tax_arrears
        })
        
        # 计算各类别总分
        tech_base_scores = [
            tech_patent_application_ratio, tech_patent_rejection_rate, tech_patent_application_stability,
            tech_patent_authorization_rate, tech_patent_maintenance_rate, tech_patent_authorization_stability,
            tech_patent_authorization_ranking, tech_software_copyright_ranking, tech_patent_concentration,
            tech_external_patent_ratio, tech_patent_continuity
        ]
        tech_score = sum(tech_base_scores)

        tech_adj_scores = [
            tech_adj_patent_outflow, tech_adj_pct_patent, tech_adj_ip_pledge,
            tech_adj_ip_transformation, tech_adj_tech_achievement
        ]
        tech_adj_score = sum(tech_adj_scores)

        dev_base_scores = [dev_talent_stability, dev_equity_financing, dev_enterprise_honor]
        dev_score = sum(dev_base_scores)

        dev_adj_scores = [dev_adj_employee_shareholding, dev_adj_honor_cancellation]
        dev_adj_score = sum(dev_adj_scores)

        oper_base_scores = [oper_capital_paid_ratio, oper_key_personnel_change, oper_equity_change_frequency]
        oper_score = sum(oper_base_scores)

        oper_adj_scores = [
            oper_adj_capital_reduction, oper_adj_equity_structure, oper_adj_related_party_change,
            oper_adj_business_status, oper_adj_revocation
        ]
        oper_adj_score = sum(oper_adj_scores)

        risk_adj_scores = [
            risk_adj_execution_restriction, risk_adj_financial_litigation,
            risk_adj_environmental_penalty, risk_adj_tax_arrears
        ]
        risk_adj_score = sum(risk_adj_scores)

        # 计算综合评分 (先标准化各类别得分，再加权)
        # 标准化到0-100分
        tech_score_normalized = (tech_score / 88) * 100  # 科技评分最高88分
        dev_score_normalized = (dev_score / 20) * 100    # 发展评分最高20分
        oper_score_normalized = (oper_score / 15) * 100  # 经营评分最高15分

        # 计算基础综合评分
        base_comprehensive_score = (
            tech_score_normalized * 0.4 +  # 科技评分权重40%
            dev_score_normalized * 0.3 +   # 发展评分权重30%
            oper_score_normalized * 0.3     # 经营评分权重30%
        )

        # 添加调整分数
        comprehensive_score = base_comprehensive_score + tech_adj_score + dev_adj_score + oper_adj_score + risk_adj_score

        # 确保综合评分在合理范围内
        comprehensive_score = max(0, min(100, comprehensive_score))

        # 确定科创健康行等级
        if comprehensive_score >= 80:
            innovation_health_level = "优秀"
        elif comprehensive_score >= 65:
            innovation_health_level = "良好"
        elif comprehensive_score >= 50:
            innovation_health_level = "一般"
        elif comprehensive_score >= 35:
            innovation_health_level = "较差"
        else:
            innovation_health_level = "差"

        # 生成企业标签 (基于综合评分)
        if comprehensive_score >= 60:
            bad_probability = 0.1   # 高分企业坏企业概率低
        elif comprehensive_score >= 45:
            bad_probability = 0.3   # 中等分数
        elif comprehensive_score >= 30:
            bad_probability = 0.6   # 较低分数
        else:
            bad_probability = 0.9   # 低分企业坏企业概率高

        label = 1 if random.random() < bad_probability else 0

        # 添加计算结果到行数据
        row.update({
            'tech_score': round(tech_score, 2),
            'tech_adj_score': round(tech_adj_score, 2),
            'dev_score': round(dev_score, 2),
            'dev_adj_score': round(dev_adj_score, 2),
            'oper_score': round(oper_score, 2),
            'oper_adj_score': round(oper_adj_score, 2),
            'risk_adj_score': round(risk_adj_score, 2),
            'comprehensive_score': round(comprehensive_score, 2),
            'innovation_health_level': innovation_health_level,
            'label': label
        })

        data_rows.append(row)

    # 创建DataFrame
    df = pd.DataFrame(data_rows)

    # 添加数据生成时间戳
    df['data_generate_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    # 保存到CSV文件
    output_file = Path(output_dir) / "enterprise_risk_sample_data.csv"
    df.to_csv(output_file, index=False, encoding='utf-8-sig')

    # 生成数据概要报告
    print("=" * 60)
    print("🎯 企业科创健康行评估样例数据生成完成")
    print("=" * 60)
    print(f"📊 样本数量: {len(df):,} 个企业")
    print(f"📈 指标数量: 33 个真实指标")
    print(f"📁 输出路径: {output_file}")
    print()

    # 标签分布统计
    label_stats = df['label'].value_counts()
    print("📋 标签分布:")
    print(f"   好企业 (0): {label_stats[0]:,} 个 ({label_stats[0]/len(df)*100:.1f}%)")
    print(f"   坏企业 (1): {label_stats[1]:,} 个 ({label_stats[1]/len(df)*100:.1f}%)")
    print()

    # 科创健康行等级分布
    level_stats = df['innovation_health_level'].value_counts()
    print("🏆 科创健康行等级分布:")
    for level, count in level_stats.items():
        print(f"   {level}: {count:,} 个 ({count/len(df)*100:.1f}%)")
    print()

    # 企业规模分布
    size_stats = df['enterprise_size'].value_counts()
    print("🏢 企业规模分布:")
    for size, count in size_stats.items():
        print(f"   {size}: {count:,} 个 ({count/len(df)*100:.1f}%)")
    print()

    # 综合评分统计
    print("📊 综合评分统计:")
    print(f"   平均分: {df['comprehensive_score'].mean():.1f}")
    print(f"   标准差: {df['comprehensive_score'].std():.1f}")
    print(f"   最高分: {df['comprehensive_score'].max():.1f}")
    print(f"   最低分: {df['comprehensive_score'].min():.1f}")
    print()

    print("✅ 数据生成完成！可以开始进行模型训练了。")
    print("=" * 60)

    return df

if __name__ == "__main__":
    # 生成样例数据
    df = generate_enterprise_sample_data(n_samples=2000)
