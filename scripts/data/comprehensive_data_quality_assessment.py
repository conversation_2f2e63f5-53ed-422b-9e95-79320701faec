#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面数据质量评估脚本
整合了数据基本信息、分布分析、IV值计算、WOE分析等功能

使用方法:
    python scripts/data/comprehensive_data_quality_assessment.py

功能包括:
- 数据基本信息统计
- 指标分类和分布分析
- IV值计算和特征重要性评估
- 特征与标签相关性分析
- 数据质量综合评分
- 潜在问题特征识别

注意: 如需可视化图表，请使用 scripts/data/data_analyze.py
"""

import pandas as pd
import numpy as np
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

from src.innovation_model_investigation.features.binning import OptimalBinning
from src.innovation_model_investigation.features.woe_encoder import WOEEncoder
from sklearn.model_selection import train_test_split

def calculate_iv_for_all_features(df, target_col='label'):
    """计算所有特征的IV值"""
    print("\n🔍 计算所有特征的IV值...")
    
    # 获取特征列
    feature_cols = [col for col in df.columns if col.startswith(('tech_', 'dev_', 'oper_', 'risk_'))]
    
    if target_col not in df.columns:
        print(f"❌ 未找到目标列 {target_col}")
        return {}
    
    features = df[feature_cols]
    target = df[target_col]
    
    # 分割数据用于分箱
    X_train, X_test, y_train, y_test = train_test_split(
        features, target, test_size=0.3, random_state=42, stratify=target
    )
    
    # 初始化分箱器
    binning = OptimalBinning()
    
    # 执行分箱并计算IV
    binning_results = binning.fit_transform_all(X_train, y_train)
    
    # 提取IV值
    iv_results = {}
    for feature_name, result in binning_results.items():
        iv_results[feature_name] = result.get('iv', 0)
    
    return iv_results

def analyze_feature_distribution(df):
    """分析特征分布"""
    print("\n📊 特征分布分析...")
    
    feature_cols = [col for col in df.columns if col.startswith(('tech_', 'dev_', 'oper_', 'risk_'))]
    
    distribution_stats = {}
    for col in feature_cols:
        stats = {
            'mean': df[col].mean(),
            'std': df[col].std(),
            'min': df[col].min(),
            'max': df[col].max(),
            'unique_values': df[col].nunique(),
            'zero_ratio': (df[col] == 0).mean()
        }
        distribution_stats[col] = stats
    
    return distribution_stats

def analyze_label_correlation(df, target_col='label'):
    """分析特征与标签的相关性"""
    print("\n🎯 特征与标签相关性分析...")
    
    feature_cols = [col for col in df.columns if col.startswith(('tech_', 'dev_', 'oper_', 'risk_'))]
    
    correlations = {}
    for col in feature_cols:
        corr = df[col].corr(df[target_col])
        correlations[col] = corr
    
    return correlations

def comprehensive_data_assessment(data_path='data/enterprise_risk_sample_data.csv'):
    """全面数据质量评估"""
    print("🚀 开始全面数据质量评估")
    print("=" * 60)
    
    # 1. 加载数据
    try:
        df = pd.read_csv(data_path)
        print(f"✅ 数据加载成功: {data_path}")
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 2. 基本信息
    print(f"\n📋 数据基本信息:")
    print(f"   样本数量: {len(df):,}")
    print(f"   特征数量: {df.shape[1]}")
    print(f"   缺失值总数: {df.isnull().sum().sum()}")
    print(f"   重复行数: {df.duplicated().sum()}")
    
    # 3. 指标分类统计
    tech_cols = [col for col in df.columns if col.startswith('tech_') and not col.endswith('_score')]
    dev_cols = [col for col in df.columns if col.startswith('dev_') and not col.endswith('_score')]
    oper_cols = [col for col in df.columns if col.startswith('oper_') and not col.endswith('_score')]
    risk_cols = [col for col in df.columns if col.startswith('risk_')]
    
    print(f"\n📈 指标分类统计:")
    print(f"   科技指标: {len(tech_cols)} 个")
    print(f"   发展指标: {len(dev_cols)} 个")
    print(f"   经营指标: {len(oper_cols)} 个")
    print(f"   风险调整指标: {len(risk_cols)} 个")
    print(f"   总计: {len(tech_cols + dev_cols + oper_cols + risk_cols)} 个")
    
    # 4. 标签分布
    if 'label' in df.columns:
        label_dist = df['label'].value_counts()
        print(f"\n🏷️ 标签分布:")
        print(f"   好企业 (0): {label_dist[0]:,} 个 ({label_dist[0]/len(df)*100:.1f}%)")
        print(f"   坏企业 (1): {label_dist[1]:,} 个 ({label_dist[1]/len(df)*100:.1f}%)")
    
    # 5. 特征分布分析
    distribution_stats = analyze_feature_distribution(df)
    
    # 统计有问题的特征
    zero_heavy_features = [col for col, stats in distribution_stats.items() if stats['zero_ratio'] > 0.8]
    low_variance_features = [col for col, stats in distribution_stats.items() if stats['std'] < 0.1]
    
    print(f"\n⚠️ 潜在问题特征:")
    print(f"   零值过多特征 (>80%): {len(zero_heavy_features)} 个")
    print(f"   低方差特征 (std<0.1): {len(low_variance_features)} 个")
    
    # 6. IV值分析
    try:
        iv_results = calculate_iv_for_all_features(df)
        
        # 按IV值分类
        high_iv = {k: v for k, v in iv_results.items() if v >= 0.3}
        medium_iv = {k: v for k, v in iv_results.items() if 0.1 <= v < 0.3}
        low_iv = {k: v for k, v in iv_results.items() if 0.02 <= v < 0.1}
        very_low_iv = {k: v for k, v in iv_results.items() if v < 0.02}
        
        print(f"\n📊 IV值分布分析:")
        print(f"   强预测力 (IV≥0.3): {len(high_iv)} 个")
        print(f"   中等预测力 (0.1≤IV<0.3): {len(medium_iv)} 个")
        print(f"   弱预测力 (0.02≤IV<0.1): {len(low_iv)} 个")
        print(f"   无预测力 (IV<0.02): {len(very_low_iv)} 个")
        
        # 显示前10个最有价值的特征
        sorted_iv = sorted(iv_results.items(), key=lambda x: x[1], reverse=True)
        print(f"\n🏆 Top 10 最有价值特征:")
        for i, (feature, iv) in enumerate(sorted_iv[:10], 1):
            print(f"   {i:2d}. {feature}: IV={iv:.4f}")
        
        # 显示无价值特征
        if very_low_iv:
            print(f"\n❌ 无预测价值特征 (前10个):")
            for i, (feature, iv) in enumerate(list(very_low_iv.items())[:10], 1):
                print(f"   {i:2d}. {feature}: IV={iv:.4f}")
        
    except Exception as e:
        print(f"❌ IV值计算失败: {e}")
    
    # 7. 相关性分析
    try:
        correlations = analyze_label_correlation(df)
        
        # 按相关性强度分类
        strong_corr = {k: v for k, v in correlations.items() if abs(v) >= 0.3}
        medium_corr = {k: v for k, v in correlations.items() if 0.1 <= abs(v) < 0.3}
        weak_corr = {k: v for k, v in correlations.items() if abs(v) < 0.1}
        
        print(f"\n🔗 特征与标签相关性:")
        print(f"   强相关 (|r|≥0.3): {len(strong_corr)} 个")
        print(f"   中等相关 (0.1≤|r|<0.3): {len(medium_corr)} 个")
        print(f"   弱相关 (|r|<0.1): {len(weak_corr)} 个")
        
        if strong_corr:
            print(f"\n💪 强相关特征:")
            for feature, corr in sorted(strong_corr.items(), key=lambda x: abs(x[1]), reverse=True):
                print(f"   {feature}: r={corr:.4f}")
        
    except Exception as e:
        print(f"❌ 相关性分析失败: {e}")
    
    # 8. 数据质量评级
    print(f"\n🎯 数据质量评级:")
    
    # 计算质量分数
    quality_score = 0
    max_score = 100
    
    # IV值质量 (40分)
    if 'iv_results' in locals():
        good_iv_ratio = (len(medium_iv) + len(high_iv)) / len(iv_results) if iv_results else 0
        iv_score = min(40, good_iv_ratio * 80)  # 最高40分
        quality_score += iv_score
        print(f"   IV值质量: {iv_score:.1f}/40 (有效特征比例: {good_iv_ratio:.1%})")
    
    # 标签平衡性 (20分)
    if 'label' in df.columns:
        label_balance = min(label_dist) / max(label_dist)
        balance_score = min(20, label_balance * 40)  # 最高20分
        quality_score += balance_score
        print(f"   标签平衡性: {balance_score:.1f}/20 (平衡度: {label_balance:.2f})")
    
    # 数据完整性 (20分)
    completeness = 1 - (df.isnull().sum().sum() / (len(df) * len(df.columns)))
    completeness_score = completeness * 20
    quality_score += completeness_score
    print(f"   数据完整性: {completeness_score:.1f}/20 (完整度: {completeness:.1%})")
    
    # 特征多样性 (20分)
    non_zero_ratio = 1 - len(zero_heavy_features) / len(tech_cols + dev_cols + oper_cols + risk_cols)
    diversity_score = non_zero_ratio * 20
    quality_score += diversity_score
    print(f"   特征多样性: {diversity_score:.1f}/20 (非零特征比例: {non_zero_ratio:.1%})")
    
    print(f"\n🏆 总体质量评分: {quality_score:.1f}/100")
    
    if quality_score >= 80:
        grade = "优秀 ⭐⭐⭐⭐⭐"
    elif quality_score >= 60:
        grade = "良好 ⭐⭐⭐⭐"
    elif quality_score >= 40:
        grade = "一般 ⭐⭐⭐"
    elif quality_score >= 20:
        grade = "较差 ⭐⭐"
    else:
        grade = "很差 ⭐"
    
    print(f"   质量等级: {grade}")
    
    print("\n" + "=" * 60)
    print("✅ 数据质量评估完成")
    
    return {
        'basic_info': {
            'samples': len(df),
            'features': df.shape[1],
            'missing_values': df.isnull().sum().sum(),
            'duplicates': df.duplicated().sum()
        },
        'iv_results': iv_results if 'iv_results' in locals() else {},
        'correlations': correlations if 'correlations' in locals() else {},
        'quality_score': quality_score,
        'quality_grade': grade
    }

if __name__ == "__main__":
    comprehensive_data_assessment()
