#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征工程增强脚本
基于优化建议，快速实施特征交互、趋势分析等高价值特征
"""

import sys
import os
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.feature_selection import mutual_info_classif
import warnings
warnings.filterwarnings('ignore')

def load_latest_data():
    """加载最新的企业数据"""
    data_path = "data/enterprise_risk_sample_data.csv"
    if not os.path.exists(data_path):
        print("❌ 数据文件不存在！")
        return None
    
    df = pd.read_csv(data_path)
    print(f"📊 加载数据: {len(df)} 个样本, {len(df.columns)} 个特征")
    return df

def create_interaction_features(df):
    """创建交互特征"""
    print("\n🔗 创建交互特征...")
    
    interaction_features = {}
    
    # 1. 财务健康 × 市场地位
    if 'financial_ratio_debt' in df.columns and 'operation_market_share' in df.columns:
        interaction_features['debt_market_interaction'] = (
            df['financial_ratio_debt'] * df['operation_market_share']
        )
        print("  ✅ 债务比率 × 市场份额")
    
    # 2. 创新能力 × 团队实力
    innovation_cols = [col for col in df.columns if 'innovation' in col.lower()]
    team_cols = [col for col in df.columns if 'team' in col.lower() or 'management' in col.lower()]
    
    if innovation_cols and team_cols:
        # 取第一个可用的创新和团队指标
        innovation_col = innovation_cols[0]
        team_col = team_cols[0]
        interaction_features['innovation_team_synergy'] = (
            df[innovation_col] * df[team_col]
        )
        print(f"  ✅ {innovation_col} × {team_col}")
    
    # 3. 财务增长 × 行业地位
    growth_cols = [col for col in df.columns if 'growth' in col.lower()]
    if growth_cols and 'operation_market_share' in df.columns:
        growth_col = growth_cols[0]
        interaction_features['growth_market_interaction'] = (
            df[growth_col] * df['operation_market_share']
        )
        print(f"  ✅ {growth_col} × 市场份额")
    
    # 4. 现金流 × 债务比率（流动性风险）
    if 'financial_cash_flow' in df.columns and 'financial_ratio_debt' in df.columns:
        interaction_features['liquidity_risk'] = (
            df['financial_cash_flow'] / (df['financial_ratio_debt'] + 1e-6)
        )
        print("  ✅ 现金流 / 债务比率 (流动性风险)")
    
    return interaction_features

def create_trend_features(df):
    """创建趋势特征（模拟时间序列）"""
    print("\n📈 创建趋势特征...")
    
    trend_features = {}
    
    # 财务指标相对强度
    financial_cols = [col for col in df.columns if 'financial' in col.lower()]
    if len(financial_cols) >= 3:
        # 计算财务综合得分
        financial_score = df[financial_cols[:3]].mean(axis=1)
        trend_features['financial_strength'] = financial_score
        
        # 财务稳定性（方差的倒数）
        financial_stability = 1 / (df[financial_cols[:3]].std(axis=1) + 1e-6)
        trend_features['financial_stability'] = financial_stability
        print("  ✅ 财务综合强度和稳定性")
    
    # 运营效率指标
    operation_cols = [col for col in df.columns if 'operation' in col.lower()]
    if len(operation_cols) >= 2:
        operation_efficiency = df[operation_cols[:2]].mean(axis=1)
        trend_features['operation_efficiency'] = operation_efficiency
        print("  ✅ 运营效率综合指标")
    
    # 外部环境适应性
    external_cols = [col for col in df.columns if 'external' in col.lower()]
    if len(external_cols) >= 1:
        # 假设外部环境越好，企业风险越低
        trend_features['external_adaptation'] = df[external_cols[0]]
        print("  ✅ 外部环境适应性")
    
    return trend_features

def create_ratio_features(df):
    """创建比率特征"""
    print("\n📊 创建比率特征...")
    
    ratio_features = {}
    
    # 1. 创新投入效率
    innovation_cols = [col for col in df.columns if 'innovation' in col.lower()]
    financial_cols = [col for col in df.columns if 'financial' in col.lower() and 'revenue' in col.lower()]
    
    if innovation_cols and financial_cols:
        innovation_efficiency = df[innovation_cols[0]] / (df[financial_cols[0]] + 1e-6)
        ratio_features['innovation_efficiency'] = innovation_efficiency
        print("  ✅ 创新投入效率")
    
    # 2. 市场竞争力
    market_cols = [col for col in df.columns if 'market' in col.lower()]
    if len(market_cols) >= 1:
        # 相对市场地位（假设行业平均为0.5）
        relative_market_position = df[market_cols[0]] / 0.5
        ratio_features['relative_market_position'] = relative_market_position
        print("  ✅ 相对市场竞争力")
    
    # 3. 风险调整后收益
    if 'financial_growth_revenue' in df.columns and 'financial_ratio_debt' in df.columns:
        risk_adjusted_return = df['financial_growth_revenue'] / (df['financial_ratio_debt'] + 1e-6)
        ratio_features['risk_adjusted_return'] = risk_adjusted_return
        print("  ✅ 风险调整后收益")
    
    return ratio_features

def create_composite_features(df):
    """创建复合特征"""
    print("\n🎯 创建复合特征...")
    
    composite_features = {}
    
    # 1. 企业健康度综合评分
    key_health_indicators = []
    for indicator in ['financial_cash_flow', 'financial_growth_revenue', 'operation_market_share']:
        if indicator in df.columns:
            key_health_indicators.append(indicator)
    
    if len(key_health_indicators) >= 2:
        health_score = df[key_health_indicators].mean(axis=1)
        composite_features['enterprise_health_score'] = health_score
        print("  ✅ 企业健康度综合评分")
    
    # 2. 创新驱动力指数
    innovation_indicators = [col for col in df.columns if 'innovation' in col.lower()]
    team_indicators = [col for col in df.columns if 'team' in col.lower() or 'management' in col.lower()]
    
    all_innovation_cols = innovation_indicators + team_indicators
    if len(all_innovation_cols) >= 2:
        innovation_index = df[all_innovation_cols[:2]].mean(axis=1)
        composite_features['innovation_drive_index'] = innovation_index
        print("  ✅ 创新驱动力指数")
    
    # 3. 风险抵御能力
    risk_resistance_cols = []
    for col in ['financial_cash_flow', 'external_regulatory_compliance']:
        if col in df.columns:
            risk_resistance_cols.append(col)
    
    if len(risk_resistance_cols) >= 1:
        risk_resistance = df[risk_resistance_cols].mean(axis=1)
        composite_features['risk_resistance'] = risk_resistance
        print("  ✅ 风险抵御能力")
    
    return composite_features

def evaluate_feature_importance(df, new_features, target_col='label'):
    """评估新特征的重要性"""
    if target_col not in df.columns:
        print("⚠️ 未找到标签列，跳过特征重要性评估")
        return {}
    
    print("\n🔍 评估新特征重要性...")
    
    feature_scores = {}
    y = df[target_col]
    
    for feature_name, feature_values in new_features.items():
        try:
            # 处理缺失值和无穷值
            feature_clean = pd.Series(feature_values).fillna(0)
            feature_clean = feature_clean.replace([np.inf, -np.inf], 0)
            
            # 计算互信息
            mi_score = mutual_info_classif(feature_clean.values.reshape(-1, 1), y, random_state=42)[0]
            feature_scores[feature_name] = mi_score
            
        except Exception as e:
            print(f"  ⚠️ 特征 {feature_name} 评估失败: {e}")
            feature_scores[feature_name] = 0
    
    # 排序并显示结果
    sorted_features = sorted(feature_scores.items(), key=lambda x: x[1], reverse=True)
    
    print("\n📊 新特征重要性排名:")
    for i, (feature, score) in enumerate(sorted_features[:10], 1):
        print(f"  {i:2d}. {feature:<30} {score:.4f}")
    
    return feature_scores

def save_enhanced_features(df, all_new_features, output_path=None):
    """保存增强后的特征数据"""
    if output_path is None:
        output_path = "data/enterprise_risk_sample_data_enhanced.csv"
    
    print(f"\n💾 保存增强特征数据...")
    
    # 创建增强数据集
    enhanced_df = df.copy()
    
    # 添加所有新特征
    for feature_name, feature_values in all_new_features.items():
        enhanced_df[feature_name] = feature_values
    
    # 保存
    enhanced_df.to_csv(output_path, index=False)
    print(f"  ✅ 已保存到: {output_path}")
    print(f"  📊 原始特征: {len(df.columns)}, 新增特征: {len(all_new_features)}, 总计: {len(enhanced_df.columns)}")
    
    return enhanced_df

def main():
    print("🚀 特征工程增强工具")
    print("=" * 50)
    
    # 1. 加载数据
    df = load_latest_data()
    if df is None:
        return
    
    # 2. 创建各类新特征
    all_new_features = {}
    
    # 交互特征
    interaction_features = create_interaction_features(df)
    all_new_features.update(interaction_features)
    
    # 趋势特征
    trend_features = create_trend_features(df)
    all_new_features.update(trend_features)
    
    # 比率特征
    ratio_features = create_ratio_features(df)
    all_new_features.update(ratio_features)
    
    # 复合特征
    composite_features = create_composite_features(df)
    all_new_features.update(composite_features)
    
    print(f"\n✅ 总计创建 {len(all_new_features)} 个新特征")
    
    # 3. 评估特征重要性
    feature_scores = evaluate_feature_importance(df, all_new_features)
    
    # 4. 保存增强数据
    enhanced_df = save_enhanced_features(df, all_new_features)
    
    # 5. 生成特征工程报告
    report_path = "outputs/feature_engineering_report.md"
    os.makedirs(os.path.dirname(report_path), exist_ok=True)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("# 特征工程增强报告\n\n")
        f.write(f"**生成时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("## 📊 特征统计\n\n")
        f.write(f"- 原始特征数量: {len(df.columns)}\n")
        f.write(f"- 新增特征数量: {len(all_new_features)}\n")
        f.write(f"- 总特征数量: {len(enhanced_df.columns)}\n\n")
        
        f.write("## 🔗 新增特征类型\n\n")
        f.write(f"- 交互特征: {len(interaction_features)} 个\n")
        f.write(f"- 趋势特征: {len(trend_features)} 个\n")
        f.write(f"- 比率特征: {len(ratio_features)} 个\n")
        f.write(f"- 复合特征: {len(composite_features)} 个\n\n")
        
        if feature_scores:
            f.write("## 🎯 特征重要性排名\n\n")
            f.write("| 排名 | 特征名称 | 重要性得分 |\n")
            f.write("|------|----------|------------|\n")
            
            sorted_features = sorted(feature_scores.items(), key=lambda x: x[1], reverse=True)
            for i, (feature, score) in enumerate(sorted_features, 1):
                f.write(f"| {i} | {feature} | {score:.4f} |\n")
        
        f.write("\n## 💡 使用建议\n\n")
        f.write("1. 重点关注重要性得分 > 0.01 的特征\n")
        f.write("2. 在模型训练中逐步加入新特征，观察性能变化\n")
        f.write("3. 定期重新评估特征重要性，剔除低价值特征\n")
        f.write("4. 考虑特征之间的相关性，避免过度拟合\n")
    
    print(f"📄 特征工程报告已保存到: {report_path}")
    print("\n🎉 特征工程增强完成！")
    print("\n💡 下一步建议:")
    print("  1. 使用增强后的数据重新训练模型")
    print("  2. 对比新旧模型的性能表现")
    print("  3. 分析高重要性特征的业务含义")

if __name__ == "__main__":
    main()
