# 脚本工具目录

本目录包含企业风险评估项目的各种工具脚本，用于数据生成、分析、评估和验证。

## 📁 目录结构

```
scripts/
├── generate_sample_data.py         # 生成样本数据
├── analyze_data.py                # 样本分布与特征现状分析
├── sample_quality_assessment.py   # 样本质量与建模潜力评估
├── compare_scorecard_performance.py # 模型与人工体系判别力自动对比
├── data_analysis_utils.py         # 数据分析工具函数
├── data_conversion/               # 数据转换工具包
│   └── __init__.py
└── README.md                      # 本说明文档
```

## 🚀 脚本使用指南

### 1. 数据生成
```bash
python scripts/generate_sample_data.py
```
- 生成企业风险评估样本数据，输出到 data/enterprise_risk_sample_data.csv。

### 2. 样本分布与特征现状分析
```bash
python scripts/analyze_data.py
```
- 分析标签分布、特征相关性、数据质量、风险分层等，输出可视化图表。

### 3. 样本质量与建模潜力评估
```bash
python scripts/sample_quality_assessment.py
```
- 评估关键特征与标签相关性、风险分层单调性、预估AUC等，判断数据是否适合建模。

### 4. 模型与人工体系判别力自动对比
```bash
python scripts/compare_scorecard_performance.py
```
- 自动对比“人工总分”与“模型分数”在全量、训练集、测试集上的判别力（AUC、KS等），并输出六条ROC曲线对比图。

## 📋 脚本功能说明

### `generate_sample_data.py`
- **功能**：生成企业风险评估样本数据
- **输出**：`data/enterprise_risk_sample_data.csv`
- **配置**：可调整样本数量、风险分布等参数

### `analyze_data.py`
- **功能**：分析样本分布与特征现状
- **内容**：标签分布、特征相关性、数据质量、风险分层、可视化输出
- **适用场景**：数据质量监控、特征工程指导

### `sample_quality_assessment.py`
- **功能**：样本质量与建模潜力评估
- **内容**：关键特征与标签相关性、风险分层单调性、预估AUC等
- **适用场景**：建模前数据体检、修复效果评估

### `compare_scorecard_performance.py`
- **功能**：模型体系与人工体系判别力自动对比
- **内容**：自动输出全量、训练集、测试集三种集合下人工分数与模型分数的AUC/KS及六条ROC曲线
- **适用场景**：权重体系优化、模型迭代、业务解释

### `data_analysis_utils.py`
- **功能**：数据分析工具函数库
- **内容**：相关性计算、风险分层、质量检查等
- **适用场景**：为其他脚本提供基础分析能力

## 💡 使用建议

1. **开发阶段**：频繁使用 `analyze_data.py` 监控数据质量
2. **建模前体检**：用 `sample_quality_assessment.py` 评估数据建模潜力
3. **权重体系优化**：用 `compare_scorecard_performance.py` 自动对比人工与模型体系效果
4. **数据转换/清洗**：使用 `data_conversion/` 下的工具处理真实业务数据

## ⚠️ 注意事项

- 所有脚本都假设在项目根目录下运行
- 确保虚拟环境已激活：`source .venv/bin/activate`
- 数据文件路径基于项目根目录的相对路径
- 运行前确保 `data/` 目录存在

## 🔧 依赖要求

脚本依赖项目的主要依赖包：
- pandas
- numpy  
- scikit-learn
- matplotlib
- seaborn

安装依赖：
```bash
pip install -e .
``` 