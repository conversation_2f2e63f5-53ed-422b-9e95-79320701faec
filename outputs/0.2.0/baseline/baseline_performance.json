{"version": "0.2.0", "generated_time": "2025-07-14T10:30:59.642922", "model_performance": {"train_auc": 1.0, "test_auc": 1.0, "test_ks": 1.0, "test_accuracy": 1.0, "test_precision": 1.0, "test_recall": 1.0, "test_f1": 1.0}, "traditional_performance": {"auc": 0.99998778998779, "improvement_over_traditional": 1.2210012209967758e-05, "improvement_pct": 0.001221016129626662}, "data_summary": {"total_samples": 2000, "train_samples": 1400, "test_samples": 600, "positive_rate": 0.3495, "feature_count": 33}, "detailed_evaluation": {"confusion_matrix": {"true_negative": 390, "false_positive": 0, "false_negative": 0, "true_positive": 210}, "classification_report": {}, "feature_importance": [{"feature_name": "tech_patent_rejection_rate", "coefficient": -2.3012219634991036, "abs_coefficient": 2.3012219634991036}, {"feature_name": "oper_capital_paid_ratio", "coefficient": -2.148502807490827, "abs_coefficient": 2.148502807490827}, {"feature_name": "tech_patent_maintenance_rate", "coefficient": -1.8908953477872807, "abs_coefficient": 1.8908953477872807}, {"feature_name": "dev_enterprise_honor", "coefficient": -1.862739522201182, "abs_coefficient": 1.862739522201182}, {"feature_name": "oper_key_personnel_change", "coefficient": -0.9848853292351114, "abs_coefficient": 0.9848853292351114}, {"feature_name": "dev_talent_stability", "coefficient": -0.9662368391219395, "abs_coefficient": 0.9662368391219395}, {"feature_name": "tech_patent_authorization_rate", "coefficient": -0.9105114887981346, "abs_coefficient": 0.9105114887981346}, {"feature_name": "tech_patent_application_stability", "coefficient": -0.6812192325108104, "abs_coefficient": 0.6812192325108104}, {"feature_name": "tech_patent_application_ratio", "coefficient": -0.4134503826615131, "abs_coefficient": 0.4134503826615131}, {"feature_name": "tech_external_patent_ratio", "coefficient": 0.0, "abs_coefficient": 0.0}]}, "data_quality": {"iv_above_0_1": 9, "iv_above_0_3": 9, "total_features": 33, "avg_iv": 0.9668239391546571}}