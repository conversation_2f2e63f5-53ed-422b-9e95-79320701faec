{"version": "0.2.0", "generated_time": "2025-07-14T10:22:13.037759", "overall_comparison": {"model_auc": 0.6786604768237082, "traditional_auc": 0.7597893712006079, "improvement": -0.08112889437689963, "improvement_pct": -10.677813806305414, "winner": "traditional"}, "subset_analysis": {"all": {"subset_name": "全量样本", "sample_count": 2000, "traditional_auc": 0.7544169649080048, "model_auc": 0.6876381637740464, "auc_improvement": -0.0667788011339584, "improvement_pct": -8.851709895217109}, "train": {"subset_name": "训练集", "sample_count": 1400, "traditional_auc": 0.7525953708794806, "model_auc": 0.6918076174517145, "auc_improvement": -0.0607877534277661, "improvement_pct": -8.07708308871602}, "test": {"subset_name": "测试集", "sample_count": 600, "traditional_auc": 0.7597893712006079, "model_auc": 0.6786604768237082, "auc_improvement": -0.08112889437689963, "improvement_pct": -10.677813806305414}}, "data_sources": {"evaluation_results": "evaluation/evaluation_results.json", "compare_data": "evaluation/model_scorecard_compare.csv", "feature_weights": "feature/feature_weights.csv", "iv_ranking": "binning/iv_ranking.csv"}, "usage_note": "此文件整合了所有对比相关数据，供对比脚本使用"}