{"version": "0.2.0", "generated_time": "2025-07-14T10:30:59.647994", "overall_comparison": {"model_auc": 1.0, "traditional_auc": 0.99998778998779, "improvement": 1.2210012209967758e-05, "improvement_pct": 0.001221016129626662, "winner": "model"}, "subset_analysis": {"all": {"subset_name": "全量样本", "sample_count": 2000, "traditional_auc": 0.9999857048446282, "model_auc": 0.9999978007453274, "auc_improvement": 1.2095900699171658e-05, "improvement_pct": 0.0012096073614520364}, "train": {"subset_name": "训练集", "sample_count": 1400, "traditional_auc": 0.9999842865769206, "model_auc": 0.9999977552252743, "auc_improvement": 1.3468648353720525e-05, "improvement_pct": 0.001346885999553571}, "test": {"subset_name": "测试集", "sample_count": 600, "traditional_auc": 0.99998778998779, "model_auc": 1.0, "auc_improvement": 1.2210012209967758e-05, "improvement_pct": 0.001221016129626662}}, "data_sources": {"evaluation_results": "evaluation/evaluation_results.json", "compare_data": "evaluation/model_scorecard_compare.csv", "feature_weights": "feature/feature_weights.csv", "iv_ranking": "binning/iv_ranking.csv"}, "usage_note": "此文件整合了所有对比相关数据，供对比脚本使用"}