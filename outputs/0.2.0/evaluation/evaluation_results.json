{"classification_metrics": {"auc": 1.0, "ks": 1.0, "accuracy": 1.0, "precision": 1.0, "recall": 1.0, "specificity": 1.0, "f1_score": 1.0}, "confusion_matrix": {"true_negative": 390, "false_positive": 0, "false_negative": 0, "true_positive": 210}, "optimal_threshold": 0.7620051716070408, "roc_data": {"fpr": [0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.1, 0.10512820512820513, 0.14102564102564102, 0.14615384615384616, 0.2128205128205128, 0.21794871794871795, 0.2564102564102564, 0.26153846153846155, 0.26666666666666666, 0.2717948717948718, 0.2743589743589744, 0.2794871794871795, 0.28974358974358977, 0.2948717948717949, 0.30256410256410254, 0.3076923076923077, 0.3384615384615385, 0.3435897435897436, 0.3564102564102564, 0.3641025641025641, 0.36923076923076925, 0.37435897435897436, 0.37948717948717947, 0.3871794871794872, 0.40512820512820513, 0.41025641025641024, 0.4282051282051282, 0.43333333333333335, 0.43846153846153846, 0.44358974358974357, 0.46153846153846156, 0.4666666666666667, 0.49230769230769234, 0.5025641025641026, 0.5051282051282051, 0.5102564102564102, 0.5128205128205128, 0.5230769230769231, 0.5384615384615384, 0.5435897435897435, 0.558974358974359, 0.5641025641025641, 0.5769230769230769, 0.5871794871794872, 0.6051282051282051, 0.6102564102564103, 0.6358974358974359, 0.6461538461538462, 0.6564102564102564, 0.6615384615384615, 0.6692307692307692, 0.676923076923077, 0.6846153846153846, 0.7, 0.7025641025641025, 0.7076923076923077, 0.7128205128205128, 0.7282051282051282, 0.7333333333333333, 0.7435897435897436, 0.7487179487179487, 0.7538461538461538, 0.7589743589743589, 0.764102564102564, 0.7743589743589744, 0.7794871794871795, 0.7846153846153846, 0.7897435897435897, 0.7923076923076923, 0.7974358974358975, 0.8102564102564103, 0.8153846153846154, 0.841025641025641, 0.8461538461538461, 0.8487179487179487, 0.8538461538461538, 0.8589743589743589, 0.8717948717948718, 0.8743589743589744, 0.8794871794871795, 0.8974358974358975, 0.9025641025641026, 0.9051282051282051, 0.9179487179487179, 0.9205128205128205, 0.9256410256410257, 0.941025641025641, 0.9487179487179487, 0.958974358974359, 0.9615384615384616, 0.9769230769230769, 0.9871794871794872, 0.9897435897435898, 1.0], "tpr": [0.0, 0.004761904761904762, 0.26666666666666666, 0.2761904761904762, 0.48095238095238096, 0.49047619047619045, 0.7619047619047619, 0.7714285714285715, 0.8238095238095238, 0.8333333333333334, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0], "thresholds": [Infinity, 0.9999998802345097, 0.9999856791716246, 0.9999851022682821, 0.9999009409942707, 0.999900067778811, 0.998736947700169, 0.9987330669707063, 0.9974190679153699, 0.9974050754306878, 0.7620051716070408, 0.0026139737824831797, 0.0025624604139860346, 0.0015129828611929316, 0.0014150566816961726, 0.0007192748844603537, 0.0006836849321536726, 0.00041104264274539925, 0.000408655480302792, 0.0003877493633265232, 0.00038631438201486724, 0.0003855839084300086, 0.0003809415495279245, 0.00032757929055098013, 0.00031364149425401215, 0.0002884022469718857, 0.0002735089727354627, 0.00022253782569396624, 0.00021711587628683458, 0.000205062384282632, 0.0001992731027911898, 0.00019406103108194944, 0.0001909826489048448, 0.0001874137437220859, 0.0001835393250300878, 0.00015685637679887796, 0.00015603932672023226, 0.00013465111041113809, 0.00013169929452809895, 0.0001235590246734513, 0.00012265584955328744, 0.00010333881084531735, 9.773431503076524e-05, 8.568909715388341e-05, 8.455879981670188e-05, 8.358031390875676e-05, 8.2627416358147e-05, 7.813004843458821e-05, 7.066105987511814e-05, 5.7821489258887785e-05, 5.769278287882527e-05, 5.437910005904945e-05, 5.375910708653529e-05, 4.85374626983537e-05, 4.6114918673102786e-05, 3.9782589838168216e-05, 3.8476242726360653e-05, 3.074966526769667e-05, 2.8474713215836496e-05, 2.6533164650663903e-05, 2.6531489288538367e-05, 2.6226114208449287e-05, 2.44791432358079e-05, 2.3099925263115925e-05, 2.1795291976232136e-05, 2.114701934383096e-05, 2.0623502867911756e-05, 1.8920911081183795e-05, 1.8311497866507777e-05, 1.7062916402636193e-05, 1.522988330833571e-05, 1.4765168905995412e-05, 1.4235458263775253e-05, 1.363552012314406e-05, 1.2865408874111543e-05, 1.262670764229752e-05, 1.2167604245686854e-05, 1.1378568375003909e-05, 1.1267393061972384e-05, 1.0633715997898011e-05, 1.0342386702708998e-05, 9.52565261687044e-06, 9.085260853187765e-06, 6.363836524228582e-06, 6.1974284324125135e-06, 5.561586272663347e-06, 5.362854833019092e-06, 5.1223881250705675e-06, 5.060692329598112e-06, 4.84224151124294e-06, 4.58613838576793e-06, 2.7800859390080873e-06, 2.6468077742207993e-06, 2.632558570424059e-06, 2.4377889556193433e-06, 2.350330552044406e-06, 1.838081959887e-06, 1.7020915273431983e-06, 1.4497903552512595e-06, 1.1309976294265559e-06, 1.0473207527173783e-06, 8.854220184413746e-07, 6.182112714041576e-07, 5.915253313359387e-07, 3.803938940655053e-07]}, "lift_table": [{"bin": "(-9.619610000000001e-06, 6.1338e-06]", "total_count": 60, "good_count": 60, "bad_count": 0, "bad_rate": 0.0, "avg_proba": 0.0, "lift": 0.0}, {"bin": "(6.1338e-06, 2.296e-05]", "total_count": 61, "good_count": 61, "bad_count": 0, "bad_rate": 0.0, "avg_proba": 0.0, "lift": 0.0}, {"bin": "(2.296e-05, 5.7783e-05]", "total_count": 59, "good_count": 59, "bad_count": 0, "bad_rate": 0.0, "avg_proba": 0.0, "lift": 0.0}, {"bin": "(5.7783e-05, 0.00018354]", "total_count": 62, "good_count": 62, "bad_count": 0, "bad_rate": 0.0, "avg_proba": 0.0001, "lift": 0.0}, {"bin": "(0.00018354, 0.0006182]", "total_count": 58, "good_count": 58, "bad_count": 0, "bad_rate": 0.0, "avg_proba": 0.0003, "lift": 0.0}, {"bin": "(0.0006182, 0.0035167]", "total_count": 60, "good_count": 60, "bad_count": 0, "bad_rate": 0.0, "avg_proba": 0.0015, "lift": 0.0}, {"bin": "(0.0035167, 0.99513]", "total_count": 60, "good_count": 30, "bad_count": 30, "bad_rate": 0.5, "avg_proba": 0.4966, "lift": 1.4285714285714286}, {"bin": "(0.99513, 0.99981]", "total_count": 60, "good_count": 0, "bad_count": 60, "bad_rate": 1.0, "avg_proba": 0.9988, "lift": 2.857142857142857}, {"bin": "(0.99981, 0.99998]", "total_count": 60, "good_count": 0, "bad_count": 60, "bad_rate": 1.0, "avg_proba": 0.9999, "lift": 2.857142857142857}, {"bin": "(0.99998, 1.0]", "total_count": 60, "good_count": 0, "bad_count": 60, "bad_rate": 1.0, "avg_proba": 1.0, "lift": 2.857142857142857}], "ks_table": [{"y_true": 1, "y_proba": 0.9999998802345097, "cum_bad": 1, "cum_good": 0, "bad_rate": 0.004761904761904762, "good_rate": 0.0, "ks": 0.004761904761904762}, {"y_true": 1, "y_proba": 0.9999998733712714, "cum_bad": 2, "cum_good": 0, "bad_rate": 0.009523809523809525, "good_rate": 0.0, "ks": 0.009523809523809525}, {"y_true": 1, "y_proba": 0.999999871449441, "cum_bad": 3, "cum_good": 0, "bad_rate": 0.014285714285714285, "good_rate": 0.0, "ks": 0.014285714285714285}, {"y_true": 1, "y_proba": 0.9999998610452696, "cum_bad": 4, "cum_good": 0, "bad_rate": 0.01904761904761905, "good_rate": 0.0, "ks": 0.01904761904761905}, {"y_true": 1, "y_proba": 0.99999984514562, "cum_bad": 5, "cum_good": 0, "bad_rate": 0.023809523809523808, "good_rate": 0.0, "ks": 0.023809523809523808}, {"y_true": 1, "y_proba": 0.9999998060358738, "cum_bad": 6, "cum_good": 0, "bad_rate": 0.02857142857142857, "good_rate": 0.0, "ks": 0.02857142857142857}, {"y_true": 1, "y_proba": 0.999999798328522, "cum_bad": 7, "cum_good": 0, "bad_rate": 0.03333333333333333, "good_rate": 0.0, "ks": 0.03333333333333333}, {"y_true": 1, "y_proba": 0.9999997894051268, "cum_bad": 8, "cum_good": 0, "bad_rate": 0.0380952380952381, "good_rate": 0.0, "ks": 0.0380952380952381}, {"y_true": 1, "y_proba": 0.9999997628011082, "cum_bad": 9, "cum_good": 0, "bad_rate": 0.04285714285714286, "good_rate": 0.0, "ks": 0.04285714285714286}, {"y_true": 1, "y_proba": 0.9999995203901286, "cum_bad": 10, "cum_good": 0, "bad_rate": 0.047619047619047616, "good_rate": 0.0, "ks": 0.047619047619047616}, {"y_true": 1, "y_proba": 0.9999994520229972, "cum_bad": 11, "cum_good": 0, "bad_rate": 0.05238095238095238, "good_rate": 0.0, "ks": 0.05238095238095238}, {"y_true": 1, "y_proba": 0.9999993514125877, "cum_bad": 12, "cum_good": 0, "bad_rate": 0.05714285714285714, "good_rate": 0.0, "ks": 0.05714285714285714}, {"y_true": 1, "y_proba": 0.9999993335070149, "cum_bad": 13, "cum_good": 0, "bad_rate": 0.06190476190476191, "good_rate": 0.0, "ks": 0.06190476190476191}, {"y_true": 1, "y_proba": 0.9999992100583489, "cum_bad": 14, "cum_good": 0, "bad_rate": 0.06666666666666667, "good_rate": 0.0, "ks": 0.06666666666666667}, {"y_true": 1, "y_proba": 0.9999991998237446, "cum_bad": 15, "cum_good": 0, "bad_rate": 0.07142857142857142, "good_rate": 0.0, "ks": 0.07142857142857142}, {"y_true": 1, "y_proba": 0.9999991724136824, "cum_bad": 16, "cum_good": 0, "bad_rate": 0.0761904761904762, "good_rate": 0.0, "ks": 0.0761904761904762}, {"y_true": 1, "y_proba": 0.9999990710723099, "cum_bad": 17, "cum_good": 0, "bad_rate": 0.08095238095238096, "good_rate": 0.0, "ks": 0.08095238095238096}, {"y_true": 1, "y_proba": 0.9999989283553197, "cum_bad": 18, "cum_good": 0, "bad_rate": 0.08571428571428572, "good_rate": 0.0, "ks": 0.08571428571428572}, {"y_true": 1, "y_proba": 0.9999988539619831, "cum_bad": 19, "cum_good": 0, "bad_rate": 0.09047619047619047, "good_rate": 0.0, "ks": 0.09047619047619047}, {"y_true": 1, "y_proba": 0.9999987900037376, "cum_bad": 20, "cum_good": 0, "bad_rate": 0.09523809523809523, "good_rate": 0.0, "ks": 0.09523809523809523}]}