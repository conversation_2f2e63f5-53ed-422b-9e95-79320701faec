# 基于 Baseline 的统一对比分析报告

**生成时间**: 2025-07-14 09:52:17
**分析版本**: 0.1.0_enhanced, 0.1.0
**数据来源**: outputs/<version>/baseline/ 目录

## 📊 各版本模型 vs 传统方法对比

### 版本 0.1.0_enhanced

- **模型AUC**: 0.7002
- **传统AUC**: 0.2965
- **提升幅度**: +136.2%
- **优胜者**: model

### 版本 0.1.0

- **模型AUC**: 0.7035
- **传统AUC**: 0.2965
- **提升幅度**: +137.3%
- **优胜者**: model

## 🔄 版本演进分析

**对比版本**: 0.1.0 → 0.1.0_enhanced

| 指标 | 基线值 | 当前值 | 变化 | 变化率 | 状态 |
|------|--------|--------|------|--------|------|
| TEST_AUC | 0.7035 | 0.7002 | -0.0033 | -0.5% | 🔵 基本稳定 |
| TEST_KS | 0.3425 | 0.3445 | +0.0020 | +0.6% | 🔵 基本稳定 |
| TEST_ACCURACY | 0.6767 | 0.6683 | -0.0083 | -1.2% | 🔵 基本稳定 |
| TEST_PRECISION | 0.5429 | 0.5281 | -0.0148 | -2.7% | 🟡 轻微下降 |
| TEST_RECALL | 0.4545 | 0.4498 | -0.0048 | -1.1% | 🔵 基本稳定 |
| TEST_F1 | 0.4948 | 0.4858 | -0.0090 | -1.8% | 🔵 基本稳定 |

## 🔍 数据漂移分析

**漂移检测摘要**: 0.1.0 → 0.1.0_enhanced

- 总特征数: 15
- 稳定特征: 15 🟢
- 轻微漂移: 0 🟡
- 严重漂移: 0 🔴

✅ 所有特征数据稳定，无漂移风险

## 💡 数据来源说明

本报告基于以下 baseline 数据生成：
- `baseline_performance.json`: 模型性能基准
- `comparison_results.json`: 详细对比分析
- `model_scorecard_compare.csv`: 样本级对比数据

这些数据由 `run_pipeline.py` 自动生成，整合了 evaluation/ 目录下的所有对比相关信息。

---
*报告由基于 baseline 的统一对比系统自动生成*
