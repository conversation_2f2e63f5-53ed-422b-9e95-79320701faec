# 黄金样本分析报告 - test

**生成时间**: 2025-07-14 09:51:46

## 📊 判断结果统计

| 判断结果 | 样本数量 | 占比 | 说明 |
|----------|----------|------|------|
| 两者都正确 | 325 | 54.2% | 模型和传统方法都判断正确 |
| 两者都错误 | 172 | 28.7% | 模型和传统方法都判断错误 |
| **模型正确，传统错误** | **51** | **8.5%** | **黄金样本 ⭐** |
| 传统正确，模型错误 | 52 | 8.7% | 传统方法更优的样本 |
| **总计** | **600** | **100.0%** | 全部样本 |

## 🎯 最优阈值

| 方法 | 最优阈值 | 说明 |
|------|----------|------|
| 传统分数 | -60.8300 | 基于约登指数计算 |
| 模型分数 | 0.2579 | 基于约登指数计算 |

## 🏆 黄金样本特征分析

| 指标 | 传统分数 | 模型分数 |
|------|----------|----------|
| 最小值 | 47.16 | 0.0419 |
| 最大值 | 0.6170 | 0.6170 |
| 平均值 | 57.97 | 0.2320 |
| 标准差 | 4.97 | 0.1217 |

## 💡 核心结论

- 🎯 **模型优势明显**: 在争议样本中，模型表现优于传统方法
- 📊 **优势样本**: 51 个 vs 52 个
- 🏆 **胜率**: 49.5%
- 💎 **价值**: 这些黄金样本展示了数据驱动方法的优势

## 🔍 业务洞察

黄金样本的特点：
- 传统评分方法容易误判的'灰色地带'企业
- 需要综合多维度信息才能准确评估的复杂案例
- 体现了机器学习模型在处理非线性关系方面的优势

